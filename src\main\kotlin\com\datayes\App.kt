package com.datayes

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.jdbc.DataSourceBuilder
import org.springframework.boot.runApplication
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.jdbc.core.JdbcTemplate
import javax.sql.DataSource

/**
 * Jackson配置 (Jackson Configuration)
 *
 * 配置JSON序列化和反序列化相关设置
 */
@Configuration
class JacksonConfig {

    /**
     * 配置ObjectMapper Bean
     *
     * @return 配置好的ObjectMapper实例
     */
    @Bean
    fun objectMapper(): ObjectMapper {
        return ObjectMapper().apply {
            registerKotlinModule()
            registerModule(JavaTimeModule())
            disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        }
    }
}

/**
 * 主数据源配置 (Primary DataSource Configuration)
 * 
 * 配置应用程序的主数据源，用于血缘信息的存储和查询
 */
@Configuration
class PrimaryDataSourceConfig {

    @Value("\${spring.datasource.url}")
    private lateinit var url: String

    @Value("\${spring.datasource.username}")
    private lateinit var username: String

    @Value("\${spring.datasource.password}")
    private lateinit var password: String

    @Value("\${spring.datasource.driver-class-name}")
    private lateinit var driverClassName: String

    /**
     * 创建主数据源
     * 
     * @return 配置好的主数据源
     */
    @Bean
    @Primary
    fun primaryDataSource(): DataSource {
        return DataSourceBuilder.create()
            .url(url)
            .username(username)
            .password(password)
            .driverClassName(driverClassName)
            .build()
    }

    /**
     * 创建主JdbcTemplate
     * 
     * @param primaryDataSource 主数据源
     * @return 配置好的JdbcTemplate
     */
    @Bean
    @Primary
    fun jdbcTemplate(primaryDataSource: DataSource): JdbcTemplate {
        return JdbcTemplate(primaryDataSource)
    }
}

/**
 * 统一 API 响应格式 (Unified API Response Format)
 */
data class ApiResponse<T>(
    val success: Boolean,
    val data: T? = null,
    val message: String? = null,
    val timestamp: Long = System.currentTimeMillis()
) {
    companion object {
        fun <T> success(data: T, message: String? = null): ApiResponse<T> {
            return ApiResponse(success = true, data = data, message = message)
        }

        fun <T> error(message: String): ApiResponse<T> {
            return ApiResponse(success = false, message = message)
        }
    }
}

@SpringBootApplication
class DgpLineageCollectorApplication

fun main(args: Array<String>) {
    runApplication<DgpLineageCollectorApplication>(*args)
}
