-- Alternative approach: Separate tables for different job types

-- Base table for common processing history fields
CREATE TABLE job_processing_history_base (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    job_key VARCHAR(255) NOT NULL COMMENT '作业唯一标识',
    job_type ENUM('DATA_EXCHANGE', 'HD<PERSON>_SHELL_SCRIPT') NOT NULL COMMENT '作业类型',
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processing_result ENUM('NO_CHANGE', 'UPDATED', 'FAILED') NOT NULL,
    changes_detected BOOLEAN DEFAULT FALSE,
    processing_duration_ms BIGINT,
    lineage_hash VARCHAR(64) COMMENT '血缘内容哈希',
    error_message TEXT,
    
    INDEX idx_job_key (job_key),
    INDEX idx_job_type (job_type),
    INDEX idx_processed_at (processed_at),
    INDEX idx_lineage_hash (lineage_hash)
) COMMENT='作业处理历史基础表';

-- Data Exchange specific processing history
CREATE TABLE data_exchange_processing_history (
    id BIGINT PRIMARY KEY,
    reader_job_id VARCHAR(255) NOT NULL COMMENT '读取作业ID',
    write_job_id VARCHAR(255) NOT NULL COMMENT '写入作业ID',
    
    FOREIGN KEY (id) REFERENCES job_processing_history_base(id) ON DELETE CASCADE,
    INDEX idx_reader_job_id (reader_job_id),
    INDEX idx_write_job_id (write_job_id)
) COMMENT='数据交换作业处理历史详情';

-- HDFS Shell Script specific processing history  
CREATE TABLE hdfs_script_processing_history (
    id BIGINT PRIMARY KEY,
    hdfs_zip_path VARCHAR(1000) NOT NULL COMMENT 'HDFS ZIP文件路径',
    script_name VARCHAR(500) NOT NULL COMMENT 'Shell脚本文件名',
    script_size_bytes BIGINT NULL COMMENT 'Shell脚本文件大小',
    extraction_time_ms BIGINT NULL COMMENT 'ZIP文件提取耗时',
    
    FOREIGN KEY (id) REFERENCES job_processing_history_base(id) ON DELETE CASCADE,
    INDEX idx_hdfs_zip_path (hdfs_zip_path),
    INDEX idx_script_name (script_name)
) COMMENT='HDFS Shell脚本作业处理历史详情';