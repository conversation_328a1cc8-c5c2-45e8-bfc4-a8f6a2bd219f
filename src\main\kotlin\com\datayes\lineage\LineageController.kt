package com.datayes.lineage

import com.datayes.ApiResponse
import com.datayes.task.TableLineageDto
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * 血缘关系控制器 (Lineage Controller)
 *
 * 提供血缘关系的 REST API
 */
@RestController
@RequestMapping("/api/lineage")
class LineageController(private val lineageService: LineageService) {

    private val logger = LoggerFactory.getLogger(LineageController::class.java)

    /**
     * 根据表ID查询上游血缘关系
     *
     * @param tableId 表ID
     * @param maxLevels 最大查询层级，默认为3
     * @return 表血缘视图列表
     */
    @GetMapping("/upstream/table/{tableId}")
    fun getUpstreamLineageByTableId(
        @PathVariable tableId: Long,
        @RequestParam(required = false, defaultValue = "3") maxLevels: Int
    ): ResponseEntity<List<TableLineageView>> {
        val result = lineageService.findUpstreamLineageByTableId(tableId, maxLevels)
        return ResponseEntity.ok(result)
    }
    
    /**
     * 根据表ID查询下游血缘关系
     *
     * @param tableId 表ID
     * @param maxLevels 最大查询层级，默认为3
     * @return 表血缘视图列表
     */
    @GetMapping("/downstream/table/{tableId}")
    fun getDownstreamLineageByTableId(
        @PathVariable tableId: Long,
        @RequestParam(required = false, defaultValue = "3") maxLevels: Int
    ): ResponseEntity<List<TableLineageView>> {
        val result = lineageService.findDownstreamLineageByTableId(tableId, maxLevels)
        return ResponseEntity.ok(result)
    }
    
    /**
     * 根据表ID查询表的上下游血缘关系及列映射 (Get table lineage with column mappings by table ID)
     * 
     * @param tableId 表ID
     * @param maxLevels 最大查询层级，默认3
     * @return 表血缘DTO，包含上下游关系和列映射
     */
    @GetMapping("/table/{tableId}/lineage-with-columns")
    fun getTableLineageWithColumnMappings(
        @PathVariable tableId: Long,
        @RequestParam(required = false, defaultValue = "3") maxLevels: Int
    ): ResponseEntity<ApiResponse<TableLineageDto>> {
        return try {
            logger.info("e7a2d8f5 | 接收到表血缘查询请求: tableId=$tableId, maxLevels=$maxLevels")
            
            val tableLineage = lineageService.findTableLineageWithColumnMappings(tableId, maxLevels)
            
            logger.info("c4f6b3a1 | 表血缘查询完成: tableId=$tableId, 上游表数=${tableLineage.upstream.size}, 下游表数=${tableLineage.downstream.size}")
            
            ResponseEntity.ok(
                ApiResponse.success(
                    data = tableLineage,
                    message = "查询成功"
                )
            )
            
        } catch (e: Exception) {
            logger.error("b9d1c7e2 | 查询表血缘关系时发生错误: tableId=$tableId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询失败: ${e.message}"))
        }
    }
    
    /**
     * 查询所有系统信息 (Query all system information with filtering)
     *
     * @param systemName 系统名称模糊搜索 (optional)
     * @param systemStatus 系统状态过滤 (optional)
     * @return 系统信息列表
     */
    @GetMapping("/systems")
    fun getAllSystems(
        @RequestParam(required = false) systemName: String?,
        @RequestParam(required = false) systemStatus: String?
    ): ResponseEntity<ApiResponse<List<SystemInfo>>> {
        return try {
            logger.info("f8c3a2b7 | 接收到系统信息查询请求: systemName=$systemName, systemStatus=$systemStatus")

            val systems = lineageService.findAllSystems(systemName, systemStatus)

            logger.info("d5e9f1c4 | 系统信息查询完成: 共查询到${systems.size}个系统")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = systems,
                    message = "查询成功"
                )
            )

        } catch (e: Exception) {
            logger.error("a7b2c8d3 | 查询系统信息时发生错误", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询失败: ${e.message}"))
        }
    }
}
