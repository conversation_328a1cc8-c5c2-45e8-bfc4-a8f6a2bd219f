**Objective:**
To read a given shell script, identify `hive -e "SQL_QUERY"` commands, and output the `SQL_QUERY` part with any shell variables within it substituted with their defined values.

**Core Approach:**
The solution processes the shell script line by line, maintaining a map of shell variables encountered. When a `hive -e` command is found, it extracts the SQL block, performs variable substitution, and adds the processed SQL to a list of results.

**Key Components:**

1.  **`parseShellScriptForSql(scriptContent: String, initialVariables: Map<String, String> = emptyMap()): List<String>`**
    *   **Input:**
        *   `scriptContent`: The entire shell script as a string.
        *   `initialVariables`: An optional map to pre-define variables (e.g., for script arguments like `$1` or variables from `source`d files).
    *   **Output:** A `List<String>` where each string is a fully substituted SQL query.
    *   **Internal Logic:**
        *   Initializes a `shellVariables` map (mutable) with `initialVariables` and a default for `hive` if not present.
        *   Iterates through each line of the script:
            *   **Skips comments (`#`) and blank lines.**
            *   **Variable Assignments:**
                *   Uses `VARIABLE_ASSIGN_PATTERN` (`^\s*(export\s+)?([a-zA-Z_][a-zA-Z0-9_]*)=(.*)$`) to detect lines like `VAR=value` or `export VAR=value`.
                *   Extracts variable name and its raw value.
                *   Handles **multi-line variable values** (e.g., `VAR="line1\nline2"`) by accumulating lines until the closing quote is found.
                *   Removes surrounding single or double quotes from the raw value.
                *   **Substitutes** any existing shell variables *within the assigned value itself* using the `substituteVariables` helper (unless the value is a command substitution like `` `date` `` or `$(date)`, which are stored literally after internal variable substitution).
                *   Stores the processed variable name and value in `shellVariables`.
            *   **`source` command:** Prints an informational message; full recursive parsing of sourced files is not implemented (users can pre-load via `initialVariables`).
            *   **`hive -e "SQL..."` command:**
                *   Uses `HIVE_EXEC_START_PATTERN` (`^\s*(?:\$\{\s*hive\s*\}|hive)\s*-e\s*"(.*)$`) to detect the command.
                *   Determines the content after the opening `"` on the current line.
                *   **Handles single-line SQL:** If the command's closing `"` is on the same line, it extracts the content between the quotes.
                *   **Handles multi-line SQL:** If the closing `"` is not on the same line, it appends the rest of the current line and subsequent lines to a `StringBuilder` until a line ending with a non-escaped `"` is found. The final closing `"` is removed.
                *   The extracted raw SQL (potentially multi-line) is then passed to `substituteVariables`.
                *   The substituted SQL is added to the `extractedSqls` list.

2.  **`substituteVariables(text: String, variables: Map<String, String>): String`**
    *   **Input:** A string (e.g., a raw SQL query or a variable's value) and the current `variables` map.
    *   **Output:** The input string with all recognized shell variable placeholders (`$VAR` or `${VAR}`) replaced by their values.
    *   **Logic:**
        *   Uses `VARIABLE_USAGE_PATTERN` (`\$(\{([a-zA-Z_][a-zA-Z0-9_]*)\}|([a-zA-Z_][a-zA-Z0-9_]*))`) to find variable placeholders.
        *   Replaces them iteratively (in a `do-while` loop) to handle nested substitutions (e.g., `var1=${var2}`, `var2=value`).
        *   If a variable is not found in the map, its placeholder remains in the text.

3.  **Regex Patterns:**
    *   `VARIABLE_ASSIGN_PATTERN`: For shell variable assignments.
    *   `VARIABLE_USAGE_PATTERN`: For shell variable usage.
    *   `HIVE_EXEC_START_PATTERN`: For detecting `hive -e` commands.

**How it Handles Specifics:**

*   **Script Arguments (`$1`, `$2`, etc.):** These can be provided via the `initialVariables` map (e.g., `mapOf("1" to "some_value")`). The script's own assignments like `yesterday=$1` will then pick up these values.
*   **Command Substitutions (`` `...` `` or `$(...)`):** The parser stores the command substitution string (e.g., `` `date +%Y-%m-%d` ``) as the variable's value after substituting any shell variables *within* that command string. The final SQL will contain these command strings literally, not their executed output.
*   **Quotes:** The parser handles shell-level quoting for variable definitions and command structure. SQL-level quoting within the extracted SQL string is preserved.
*   **Escaping in Kotlin Test Strings:** For literal `$` in Kotlin raw strings (used in test cases), `${'$'}` is used to prevent Kotlin's string template interpolation.

**Limitations/Simplifications:**

*   **`source` command:** Not fully implemented; only logs detection. Variables from sourced files need to be manually provided via `initialVariables` or defined within the main script.
*   **Command Substitution Evaluation:** Does not execute shell commands within `` `...` `` or `$(...)`. These are treated as literal strings in the final SQL.
*   **Complex Shell Logic:** Does not interpret shell conditionals (`if/else`), loops (`for/while`), or shell functions beyond simple variable assignments and `hive -e` calls.
*   **Error Handling:** Basic warnings for unterminated blocks, but not exhaustive parsing error detection.