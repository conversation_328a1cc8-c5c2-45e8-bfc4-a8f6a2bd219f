package com.datayes.sql

import net.sf.jsqlparser.JSQLParserException
import net.sf.jsqlparser.expression.Expression
import net.sf.jsqlparser.parser.CCJSqlParserUtil
import net.sf.jsqlparser.schema.Column
import net.sf.jsqlparser.schema.Table
import net.sf.jsqlparser.statement.insert.Insert
import net.sf.jsqlparser.statement.select.*

/**
 * SQL解析器 (SQL Parser)，用于解析SQL查询并提取表名和列名信息
 */
object SqlParser {

    /**
     * 解析SQL查询字符串，提取表名和列名信息
     *
     * @param sqlQuery SQL查询字符串 (SQL query string)
     * @return 解析结果 (parsing result)，包含提取的表和列信息
     * @throws SqlParsingException 当SQL解析失败时抛出
     */
    fun parse(sqlQuery: String): ParseResult {
        try {
            val statement = CCJSqlParserUtil.parse(sqlQuery)

            return when (statement) {
                is Select -> parseSelectStatement(statement)
                else -> throw SqlParsingException("不支持的SQL语句类型 (Unsupported SQL statement type): ${statement.javaClass.simpleName}")
            }
        } catch (e: JSQLParserException) {
            throw SqlParsingException("SQL解析错误 (SQL parsing error): ${e.message}", e)
        } catch (e: Exception) {
            throw SqlParsingException("处理SQL时发生错误 (Error processing SQL): ${e.message}", e)
        }
    }

    /**
     * 解析数据修改语句（INSERT/UPDATE/DELETE），提取源表、目标表和列映射信息
     *
     * @param sqlQuery SQL语句字符串
     * @return 数据修改语句解析结果，包含源表、目标表和列映射信息
     * @throws SqlParsingException 当SQL解析失败时抛出
     */
    fun parseDataModification(sqlQuery: String): DataModificationResult {
        try {
            val statement = CCJSqlParserUtil.parse(sqlQuery)

            return when (statement) {
                is Insert -> parseInsertStatement(statement)
                else -> throw SqlParsingException("不支持的数据修改语句类型 (Unsupported data modification statement type): ${statement.javaClass.simpleName}")
            }
        } catch (e: JSQLParserException) {
            throw SqlParsingException("SQL解析错误 (SQL parsing error): ${e.message}", e)
        } catch (e: Exception) {
            throw SqlParsingException("处理SQL时发生错误 (Error processing SQL): ${e.message}", e)
        }
    }

    /**
     * 解析SELECT语句
     */
    private fun parseSelectStatement(select: Select): ParseResult {
        val extractedTables = mutableListOf<TableReference>()
        val extractedColumns = mutableListOf<ColumnReference>()

        // 在JSQLParser 5.2中，Select是一个接口，直接处理不同类型的select
        if (select is PlainSelect) {
            // 解析表信息
            extractTables(select, extractedTables)

            // 解析列信息
            extractColumns(select, extractedColumns)

            // 处理子查询
            extractSubqueries(select, extractedTables, extractedColumns)
        } else if (select is SetOperationList) {
            // 处理UNION, INTERSECT等操作
            for (selectItem in select.selects) {
                if (selectItem is PlainSelect) {
                    extractTables(selectItem, extractedTables)
                    extractColumns(selectItem, extractedColumns)
                    extractSubqueries(selectItem, extractedTables, extractedColumns)
                }
            }
        }

        return ParseResult(extractedTables, extractedColumns)
    }

    /**
     * 从SELECT语句中提取表信息
     */
    private fun extractTables(plainSelect: PlainSelect, extractedTables: MutableList<TableReference>) {
        // 处理FROM子句
        val fromItem = plainSelect.fromItem
        if (fromItem != null) {
            extractTableFromItem(fromItem, extractedTables)
        }

        // 处理JOIN子句
        val joins = plainSelect.joins
        if (joins != null) {
            for (join in joins) {
                val rightItem = join.rightItem
                if (rightItem != null) {
                    extractTableFromItem(rightItem, extractedTables)
                }
            }
        }
    }

    /**
     * 从FromItem中提取表信息
     */
    private fun extractTableFromItem(fromItem: FromItem, extractedTables: MutableList<TableReference>) {
        when (fromItem) {
            is Table -> {
                val schemaName = fromItem.schemaName
                val tableName = fromItem.name
                val alias = fromItem.alias?.name

                extractedTables.add(TableReference(schemaName, tableName, alias))
            }

            is ParenthesedSelect -> {
                // 处理带括号的子查询，这是JSQLParser 5.2中的新类型
                // 注意：必须在Select之前检查，因为ParenthesedSelect也是Select的子类
                val innerSelect = fromItem.select
                if (innerSelect != null) {
                    extractTablesFromSelect(innerSelect, extractedTables)
                }
            }

            is Select -> {
                // 处理子查询
                extractTablesFromSelect(fromItem, extractedTables)
            }

            is ParenthesedFromItem -> {
                // 处理括号中的FROM项
                extractTableFromItem(fromItem.fromItem, extractedTables)
                val joins = fromItem.joins
                if (joins != null) {
                    for (join in joins) {
                        extractTableFromItem(join.rightItem, extractedTables)
                    }
                }
            }
        }
    }

    /**
     * 从Select语句中提取表信息的统一方法
     */
    private fun extractTablesFromSelect(select: Select, extractedTables: MutableList<TableReference>) {
        when (select) {
            is PlainSelect -> {
                extractTables(select, extractedTables)
                extractSubqueries(select, extractedTables, mutableListOf<ColumnReference>())
            }
            is SetOperationList -> {
                // 处理UNION, INTERSECT等操作
                for (selectItem in select.selects) {
                    extractTablesFromSelect(selectItem, extractedTables)
                }
            }
            else -> {
                // 处理其他类型的Select
            }
        }
    }

    /**
     * 从SELECT语句中提取列信息
     */
    private fun extractColumns(plainSelect: PlainSelect, extractedColumns: MutableList<ColumnReference>) {
        val selectItems = plainSelect.selectItems

        for (item in selectItems) {
            // 在JSQLParser 5.2中，SelectItem是接口，需要使用when语句进行类型判断
            if (item.toString() == "*") {
                // 处理 SELECT *
                extractedColumns.add(ColumnReference("*", null, null, "*", true))
            } else if (item.toString().endsWith(".*")) {
                // 处理 SELECT table.*
                val tablePrefix = item.toString().substringBefore(".*")
                extractedColumns.add(ColumnReference("*", tablePrefix, null, "$tablePrefix.*", true))

            } else {
                // 处理普通列
                val expression = item.expression
                val alias = item.alias?.name

                when (expression) {
                    is Column -> {
                        val columnName = expression.columnName
                        val tablePrefix = expression.table?.name
                        extractedColumns.add(
                            ColumnReference(
                                columnName, tablePrefix, alias,
                                if (tablePrefix != null) "$tablePrefix.$columnName" else columnName, false
                            )
                        )
                    }

                    else -> {
                        // 处理函数和其他表达式
                        val expressionString = expression.toString()
                        extractedColumns.add(
                            ColumnReference(
                                expressionString,
                                null,
                                alias,
                                expressionString,
                                false
                            )
                        )
                    }
                }
            }
        }
    }


    /**
     * 处理子查询
     */
    private fun extractSubqueries(
        plainSelect: PlainSelect,
        extractedTables: MutableList<TableReference>,
        extractedColumns: MutableList<ColumnReference>,
    ) {
        // 处理WHERE子句中的子查询
        val whereExpression = plainSelect.where
        if (whereExpression != null) {
            extractSubqueriesFromExpression(whereExpression, extractedTables)
        }

        // 处理HAVING子句中的子查询
        val havingExpression = plainSelect.having
        if (havingExpression != null) {
            extractSubqueriesFromExpression(havingExpression, extractedTables)
        }
    }

    /**
     * 从表达式中提取子查询
     */
    private fun extractSubqueriesFromExpression(expression: Expression, extractedTables: MutableList<TableReference>) {
        // 这里需要递归处理表达式中的子查询
        // 由于JSqlParser的API限制，这部分实现可能需要更复杂的逻辑
        // 这里提供一个简化版本
        val expressionString = expression.toString()
        if (expressionString.contains("SELECT", ignoreCase = true)) {
            try {
                // 尝试提取和解析子查询
                // 注意：这是一个简化的实现，实际上需要更复杂的逻辑来正确提取子查询
                val subQueryMatches =
                    Regex("\\(\\s*SELECT.*?\\)", RegexOption.DOT_MATCHES_ALL).findAll(expressionString)

                for (match in subQueryMatches) {
                    val subQueryString = match.value.substring(1, match.value.length - 1).trim()
                    try {
                        val subQueryStatement = CCJSqlParserUtil.parse(subQueryString) as? Select
                        if (subQueryStatement != null) {
                            // 在JSQLParser 5.2中，Select是一个接口，直接处理不同类型的select
                            if (subQueryStatement is PlainSelect) {
                                extractTables(subQueryStatement, extractedTables)
                                extractSubqueries(subQueryStatement, extractedTables, mutableListOf<ColumnReference>())
                            }
                        }
                    } catch (e: Exception) {
                        // 忽略子查询解析错误
                    }
                }
            } catch (e: Exception) {
                // 忽略子查询提取错误
            }
        }
    }

    /**
     * 解析INSERT语句
     */
    private fun parseInsertStatement(insert: Insert): DataModificationResult {
        // 1. 解析目标表
        val targetTable = insert.table
        val targetTableRef = TableReference(
            schema = targetTable.schemaName,
            name = targetTable.name,
            alias = null // INSERT语句中目标表没有别名
        )

        // 2. 解析目标列（如果指定）
        val targetColumns = insert.columns?.map { it.columnName }

        // 3. 解析SELECT子句
        val select = insert.select
        if (select == null) {
            throw SqlParsingException("INSERT语句缺少SELECT子句")
        }

        // 4. 使用现有逻辑解析SELECT部分
        val selectResult = parseSelectStatement(select)
        
        // 5. 构建列映射关系
        val columnMappings = buildColumnMappings(selectResult.columns, targetColumns)

        return DataModificationResult(
            targetTable = targetTableRef,
            targetColumns = targetColumns,
            sourceTables = selectResult.tables,
            sourceColumns = selectResult.columns,
            columnMappings = columnMappings
        )
    }

    /**
     * 构建列映射关系
     */
    private fun buildColumnMappings(
        sourceColumns: List<ColumnReference>,
        targetColumns: List<String>?
    ): List<ColumnMapping> {
        return sourceColumns.mapIndexed { index, sourceColumn ->
            val targetColumnName = when {
                targetColumns != null && index < targetColumns.size -> targetColumns[index]
                sourceColumn.alias != null -> sourceColumn.alias
                else -> sourceColumn.name
            }
            
            ColumnMapping(
                sourceColumn = sourceColumn,
                targetColumnName = targetColumnName,
                targetColumnIndex = index
            )
        }
    }
}
