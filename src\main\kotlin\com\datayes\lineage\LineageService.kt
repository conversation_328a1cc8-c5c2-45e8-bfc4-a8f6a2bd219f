package com.datayes.lineage

import com.datayes.task.ColumnMappingDto
import com.datayes.task.TableLineageDto
import com.datayes.task.TableRelationshipDto
import com.datayes.task.toColumnMappingDto
import com.datayes.task.toTableRelationshipDtos
import org.springframework.stereotype.Service

/**
 * 血缘关系服务层 (Lineage Service Layer)
 *
 * 处理血缘关系的业务逻辑
 */
@Service
class LineageService(private val lineageRepository: LineageRepository) {

    /**
     * 根据表ID查询上游血缘关系
     *
     * @param tableId 表ID
     * @param maxLevels 最大查询层级
     * @return 表血缘视图列表
     */
    fun findUpstreamLineageByTableId(tableId: Long, maxLevels: Int = 3): List<TableLineageView> {
        return lineageRepository.findUpstreamLineageByTableId(tableId, maxLevels)
    }
    
    /**
     * 根据表ID查询下游血缘关系
     *
     * @param tableId 表ID
     * @param maxLevels 最大查询层级
     * @return 表血缘视图列表
     */
    fun findDownstreamLineageByTableId(tableId: Long, maxLevels: Int = 3): List<TableLineageView> {
        return lineageRepository.findDownstreamLineageByTableId(tableId, maxLevels)
    }
    
    /**
     * 根据表ID查询上下游血缘关系及列映射
     *
     * @param tableId 表ID
     * @param maxLevels 最大查询层级
     * @return 表血缘DTO，包含上下游关系和列映射
     */
    fun findTableLineageWithColumnMappings(tableId: Long, maxLevels: Int = 3): TableLineageDto {
        // 查询上游和下游表级血缘关系
        val upstreamLineage = lineageRepository.findUpstreamLineageByTableId(tableId, maxLevels)
        val downstreamLineage = lineageRepository.findDownstreamLineageByTableId(tableId, maxLevels)
        
        // 查询列级血缘关系
        val columnLineages = lineageRepository.findColumnLineageByTableId(tableId)
        
        // 转换为DTO
        val upstreamRelationships = upstreamLineage.toTableRelationshipDtos()
        val downstreamRelationships = downstreamLineage.toTableRelationshipDtos()
        
        // 为每个表关系添加列映射
        val upstreamWithColumnMappings = addColumnMappingsToRelationships(upstreamRelationships, columnLineages, true)
        val downstreamWithColumnMappings = addColumnMappingsToRelationships(downstreamRelationships, columnLineages, false)
        
        return TableLineageDto(
            tableId = tableId,
            upstream = upstreamWithColumnMappings,
            downstream = downstreamWithColumnMappings
        )
    }
    
    /**
     * 为表关系添加列映射
     *
     * @param relationships 表关系列表
     * @param columnLineages 列级血缘关系
     * @param isUpstream 是否为上游关系
     * @return 添加了列映射的表关系列表
     */
    private fun addColumnMappingsToRelationships(
        relationships: List<TableRelationshipDto>, 
        columnLineages: List<ColumnLineageView>,
        isUpstream: Boolean
    ): List<TableRelationshipDto> {
        return relationships.map { relationship ->
            val columnMappings = columnLineages.filter { columnLineage ->
                if (isUpstream) {
                    // 上游关系：源表是上游，目标表是当前表
                    columnLineage.sourceTable == relationship.sourceTable && 
                    columnLineage.sourceSchema == relationship.sourceSchema &&
                    columnLineage.targetTable == relationship.targetTable &&
                    columnLineage.targetSchema == relationship.targetSchema
                } else {
                    // 下游关系：源表是当前表，目标表是下游
                    columnLineage.sourceTable == relationship.sourceTable &&
                    columnLineage.sourceSchema == relationship.sourceSchema &&
                    columnLineage.targetTable == relationship.targetTable &&
                    columnLineage.targetSchema == relationship.targetSchema
                }
            }.map { it.toColumnMappingDto() }
            
            relationship.copy(columnMappings = columnMappings)
        }
    }
    
    /**
     * 查询所有系统信息 (Query all system information with filtering)
     *
     * @param systemName 系统名称模糊搜索 (optional)
     * @param systemStatus 系统状态过滤 (optional)
     * @return 系统信息列表
     */
    fun findAllSystems(systemName: String? = null, systemStatus: String? = null): List<SystemInfo> {
        return lineageRepository.findAllSystems(systemName, systemStatus)
    }
}
