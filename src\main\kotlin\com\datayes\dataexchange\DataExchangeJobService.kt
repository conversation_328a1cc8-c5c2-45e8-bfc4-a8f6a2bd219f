package com.datayes.dataexchange

import com.datayes.lineage.JobProcessingHistory
import com.datayes.lineage.JobProcessingHistoryRepository
import com.datayes.lineage.JobType
import com.datayes.lineage.LineageChangeDetectionService
import com.datayes.lineage.LineageRepository
import com.datayes.lineage.LineageResult
import com.datayes.lineage.ProcessingResult
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * 增强的数据交互作业服务 (Enhanced Data Exchange Job Service)
 */
@Service
class DataExchangeJobService(
    private val lineageRepository: LineageRepository,
    private val changeDetectionService: LineageChangeDetectionService,
    private val processingHistoryRepository: JobProcessingHistoryRepository,
    private val dataExchangeJobRepository: DataExchangeJobRepository,
) {

    private val logger = LoggerFactory.getLogger(DataExchangeJobService::class.java)

    /**
     * 处理所有活动作业的血缘 (Process lineage for all active jobs)
     */
    fun processAllActiveJobsLineage(): List<LineageProcessResult> {
        val listActiveDataExchangeJobs = dataExchangeJobRepository.listActiveDataExchangeJobs()
        val lineageProcessResults = mutableListOf<LineageProcessResult>()
        for (job in listActiveDataExchangeJobs) {
            val result = processJobLineageWithChangeDetection(job)
            lineageProcessResults.add(result)
        }
        return lineageProcessResults
    }


    /**
     * 智能处理作业血缘 (只在有变更时更新数据库)
     */
    fun processJobLineageWithChangeDetection(job: DataExchangeJob, taskId: Long? = null): LineageProcessResult {
        val startTime = System.currentTimeMillis()
        val jobKey = "${job.readerJobId}_${job.writeJobId}"

        return try {
            // 1. 转换为血缘信息
            val lineageResult = DataExchangeJobLineageConverter.convertToLineage(job)

            if (!lineageResult.success) {
                return createFailureResult(job, lineageResult, startTime, "血缘转换失败")
            }

            val dataLineage = lineageResult.lineage!!

            // 2. 检测变更
            val changeDetection = changeDetectionService.detectChanges(jobKey, dataLineage)

            val processingResult = if (changeDetection.hasChanges) {
                // 3a. 有变更：更新数据库
                logger.info("检测到血缘变更，更新数据库: $jobKey")
                lineageRepository.updateLineageInDatabase(jobKey, dataLineage, job, taskId)
                ProcessingResult.UPDATED
            } else {
                // 3b. 无变更：仅更新处理时间
                logger.debug("未检测到血缘变更，跳过数据库更新: $jobKey")
                ProcessingResult.NO_CHANGE
            }

            // 4. 记录处理历史
            recordProcessingHistory(
                jobKey = jobKey,
                job = job,
                result = processingResult,
                lineageHash = changeDetection.currentHash,
                processingTime = System.currentTimeMillis() - startTime
            )

            LineageProcessResult(
                job = job,
                lineageResult = lineageResult,
                processingTimeMs = System.currentTimeMillis() - startTime,
                hasChanges = changeDetection.hasChanges,
                processingResult = processingResult
            )

        } catch (e: Exception) {
            logger.error("处理作业血缘时发生异常: $jobKey", e)
            createFailureResult(job, null, startTime, "处理异常: ${e.message}")
        }
    }


    /**
     * 记录处理历史
     */
    private fun recordProcessingHistory(
        jobKey: String,
        job: DataExchangeJob,
        result: ProcessingResult,
        lineageHash: String,
        processingTime: Long
    ) {
        val history = JobProcessingHistory(
            jobKey = jobKey,
            jobType = JobType.DATA_EXCHANGE,
            readerJobId = job.readerJobId,
            writeJobId = job.writeJobId,
            processingResult = result,
            changesDetected = result == ProcessingResult.UPDATED,
            processingDurationMs = processingTime,
            lineageHash = lineageHash
        )

        processingHistoryRepository.save(history)
    }

    private fun createFailureResult(
        job: DataExchangeJob,
        lineageResult: LineageResult?,
        startTime: Long,
        errorMessage: String
    ): LineageProcessResult {
        val failureResult = lineageResult ?: LineageResult(
            lineage = null,
            warnings = emptyList(),
            errors = listOf(errorMessage),
            success = false
        )

        return LineageProcessResult(
            job = job,
            lineageResult = failureResult,
            processingTimeMs = System.currentTimeMillis() - startTime,
            hasChanges = false,
            processingResult = ProcessingResult.FAILED
        )
    }
}

/**
 * 增强的血缘处理结果 (Enhanced Lineage Process Result)
 */
data class LineageProcessResult(
    val job: DataExchangeJob,
    val lineageResult: LineageResult,
    val processingTimeMs: Long,
    val hasChanges: Boolean = false,
    val processingResult: ProcessingResult = ProcessingResult.NO_CHANGE
)