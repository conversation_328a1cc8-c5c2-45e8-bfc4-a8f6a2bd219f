# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

DGP Lineage Collector is a Spring Boot application written in Kotlin that collects and analyzes data lineage from data exchange platforms and SQL scripts. It extracts table/column dependencies from SQL queries and transforms them into standardized lineage models.

## Build Commands

```bash
# Build the project
./mvnw clean compile

# Run tests
./mvnw test

# Run a single test class
./mvnw test -Dtest=SqlParserTest

# Package application
./mvnw clean package

# Run application
./mvnw spring-boot:run
```

## Architecture

### Core Package Structure
- `com.datayes.sql/` - SQL parsing and analysis
  - `SqlParser.kt` - SQL parsing engine using JSQLParser
  - `SqlParseResult.kt` - Parse result data structures
  - `SqlParsingException.kt` - SQL parsing error handling
- `com.datayes.lineage/` - Core lineage domain
  - `Lineage.kt` - Core immutable lineage data models
  - `LineageService.kt` - Business logic for lineage operations
  - `LineageRepository.kt` - Data access for lineage entities
  - `LineageController.kt` - REST API endpoints
- `com.datayes.dataexchange/` - Data exchange platform integration
  - `DataExchangeJobService.kt` - Business logic service layer
  - `DataExchangeJobRepository.kt` - Data access layer
  - `DataExchangeJobLineageConverter.kt` - Transforms jobs to lineage models
  - `DataExchangeJobDataSource.kt` - Secondary datasource configuration
- `com.datayes.task/` - Lineage task management
  - `LineageTaskService.kt` - Task management business logic
  - `LineageTaskRepository.kt` - Task data access layer
  - `LineageTaskController.kt` - Task REST API endpoints
- `com.datayes.shell/` - Shell script parsing for Hive SQL extraction
- `com.datayes.git/` - Git repository utilities

### Key Design Patterns
- **Functional Core, Imperative Shell** - Core parsing logic is pure, side effects in shell
- **Data-Oriented Design** - Immutable Kotlin data classes throughout
- **Dual Datasource Configuration** - Separate connections for main app and data exchange platform

### Database Configuration
The application uses two MySQL datasources:
- Main database: `***********:3306/dgp` 
- Data Exchange platform: `***********:3306/dc_data_exc`

Configuration is in `DataExchangeJobDataSource.kt` with separate `JdbcTemplate` beans.

### SQL Parsing Engine
The `SqlParser` class handles complex SQL scenarios:
- JOIN operations with table aliases
- Subqueries and nested selects  
- Wildcard column selection (`*`)
- Multiple database dialects via JDBC URL parsing

## Testing

Tests use JUnit 5 with minimal Mockito usage. Key test classes:
- `SqlParserTest.kt` - Comprehensive SQL parsing scenarios
- `LineageConverterTest.kt` - Lineage transformation logic
- `DataModelTest.kt` - Data model validation
- `ShellScriptParserTest.kt` - Shell script parsing tests
- `GitUtilsTest.kt` - Git utility function tests
- `HdfsReaderTest.kt` - HDFS connectivity and file operations

### Testing Philosophy
- **Use mocks as little as possible** - Prefer real implementations and actual services
- **Integration/E2E tests** - Use the real development server, not mocks
- **Database testing** - Never delete or update data before/after tests; assume data exists
- **Real services** - Test against actual databases, HDFS clusters, and external APIs when possible
- **Mocks only when necessary** - Use mocks only for external dependencies that cannot be easily controlled or are expensive to set up

### REST API Integration Testing Rules
- **Integration test naming** - Integration test classes must end with `IT` suffix (e.g., `LineageSystemsIT`)
- **Real HTTP requests only** - Always use RestAssured for actual HTTP calls, never mock controllers
- **External application startup** - Tests assume the application is already running, do NOT use @SpringBootTest
- **Manual application management** - Boot the application manually before running integration tests
- **Base class pattern** - Extend `RestApiIntegrationTestBase` which configures RestAssured base URL
- **RestAssured + AssertJ validation** - Use RestAssured for HTTP calls and AssertJ for complex data assertions
- **Multi-layered validation** - Validate HTTP status → response structure → data types → business logic
- **JsonPath for complex validation** - Use JsonPath extraction for detailed JSON response validation
- **Type-safe assertions** - Use explicit generic types to avoid Kotlin inference issues (e.g., `hasSize<Any>(greaterThan(0))`)
- **Unique log prefixes** - Each log message has a unique UUID prefix for traceability

## Technology Stack

- **Framework**: Spring Boot 3.4.4
- **Language**: Kotlin 1.9.25 with Java 17
- **Build**: Maven with wrapper
- **SQL Parser**: JSQLParser 5.2
- **Database**: MySQL (dual datasources)
- **JSON**: Jackson with Kotlin module
- **Big Data**: Hadoop 2.7.2-TBDS (HDFS file operations only)
- **Git**: Eclipse JGit 7.2.1
- **Testing**: JUnit 5, Mockito