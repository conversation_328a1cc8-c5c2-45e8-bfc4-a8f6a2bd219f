package com.datayes.hdfs

import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * HDFS Shell Script REST API 控制器
 * 
 * 提供HDFS shell脚本处理的REST API接口
 * 类似于LineageController，专门处理HDFS相关的血缘分析请求
 */
@RestController
@RequestMapping("/api/hdfs")
class HdfsShellScriptController(
    private val hdfsShellScriptService: HdfsShellScriptService
) {
    
    private val logger = LoggerFactory.getLogger(HdfsShellScriptController::class.java)
    
    /**
     * 处理指定HDFS路径下的shell脚本血缘分析
     * 
     * @param request 处理请求
     * @return 处理结果
     */
    @PostMapping("/process")
    fun processHdfsShellScripts(@RequestBody request: HdfsProcessingRequest): ResponseEntity<HdfsProcessingResponse> {
        logger.info("2e8f7a9c | 接收到HDFS处理请求: {}", request.hdfsPath)
        
        return try {
            // 1. 处理HDFS shell脚本
            val processingResult = hdfsShellScriptService.processHdfsShellScripts(
                hdfsPath = request.hdfsPath,
                config = request.config ?: HdfsProcessingConfig()
            )
            
            if (processingResult.errors.isNotEmpty()) {
                logger.warn("f4c9e2b5 | HDFS处理过程中出现错误: {}", processingResult.errors)
            }
            
            // 2. 处理血缘分析
            val lineageResults = if (processingResult.jobs.isNotEmpty()) {
                hdfsShellScriptService.processMultipleJobsLineage(processingResult.jobs)
            } else {
                emptyList()
            }
            
            // 3. 构建响应
            val response = HdfsProcessingResponse(
                success = processingResult.errors.isEmpty(),
                message = if (processingResult.errors.isEmpty()) {
                    "成功处理${processingResult.jobs.size}个shell脚本作业"
                } else {
                    "处理完成，但存在${processingResult.errors.size}个错误"
                },
                hdfsProcessingResult = processingResult,
                lineageResults = lineageResults,
                summary = HdfsProcessingSummary(
                    totalScriptsFound = processingResult.totalJobsFound,
                    successfulLineageProcessing = lineageResults.count { it.processingResult != HdfsProcessingResult.FAILED },
                    lineageUpdated = lineageResults.count { it.processingResult == HdfsProcessingResult.UPDATED },
                    processingTimeMs = lineageResults.sumOf { it.processingTimeMs }
                )
            )
            
            logger.info("a6d3f8e7 | HDFS处理完成: 发现{}个脚本，成功处理{}个血缘", 
                       response.summary.totalScriptsFound, 
                       response.summary.successfulLineageProcessing)
            
            ResponseEntity.ok(response)
            
        } catch (e: Exception) {
            logger.error("b9e5f4c2 | HDFS处理发生异常", e)
            
            val errorResponse = HdfsProcessingResponse(
                success = false,
                message = "处理失败: ${e.message}",
                hdfsProcessingResult = HdfsShellScriptProcessingResult(
                    jobs = emptyList(),
                    hdfsPath = request.hdfsPath,
                    errors = listOf("系统异常: ${e.message}")
                ),
                lineageResults = emptyList(),
                summary = HdfsProcessingSummary()
            )
            
            ResponseEntity.status(500).body(errorResponse)
        }
    }
    
    /**
     * 获取HDFS处理配置信息
     */
    @GetMapping("/config")
    fun getDefaultConfig(): ResponseEntity<HdfsProcessingConfig> {
        return ResponseEntity.ok(HdfsProcessingConfig())
    }
    
    /**
     * 查询指定HDFS目录下的所有ZIP文件
     * 
     * @param hdfsPath HDFS目录路径
     * @param includeBackups 是否包含备份文件（带时间戳的文件），默认false
     * @return ZIP文件查询结果
     */
    @GetMapping("/zip-files")
    fun queryZipFiles(
        @RequestParam hdfsPath: String,
        @RequestParam(defaultValue = "false") includeBackups: Boolean
    ): ResponseEntity<HdfsZipFileQueryResult> {
        logger.info("8f2e4a7c | 接收到ZIP文件查询请求: hdfsPath={}, includeBackups={}", hdfsPath, includeBackups)
        
        return try {
            val result = hdfsShellScriptService.queryZipFiles(hdfsPath, includeBackups)
            
            if (result.success) {
                logger.info("d5c9e3a8 | ZIP文件查询成功: 找到{}个文件，耗时{}ms", 
                           result.totalAfterFilter, result.queryTimeMs)
                ResponseEntity.ok(result)
            } else {
                logger.warn("b7f4e2a6 | ZIP文件查询失败: {}", result.message)
                ResponseEntity.status(400).body(result)
            }
            
        } catch (e: Exception) {
            logger.error("a3e8f5c9 | ZIP文件查询发生异常", e)
            
            val errorResult = HdfsZipFileQueryResult(
                success = false,
                message = "查询失败: ${e.message}",
                hdfsPath = hdfsPath,
                zipFiles = emptyList()
            )
            
            ResponseEntity.status(500).body(errorResult)
        }
    }

    /**
     * 完整的HDFS路径处理工作流：
     * 1. 获取HDFS路径下所有非备份ZIP文件
     * 2. 解析每个ZIP中的shell脚本为DataLineage
     * 3. 将每个脚本映射为lineage_tasks表中的一行并保存
     * 4. 保存DataLineage血缘数据
     * 
     * @param request 包含HDFS路径和配置的请求
     * @return 完整的处理结果，包含LineageTask保存信息
     */
    @PostMapping("/process-and-save")
    fun processHdfsPathAndSaveLineageTasks(@RequestBody request: HdfsFullProcessingRequest): ResponseEntity<HdfsFullProcessingResponse> {
        logger.info("f8c2e4a9 | 接收到完整HDFS处理和保存请求: {}", request.hdfsPath)
        
        return try {
            val result = hdfsShellScriptService.processHdfsPathAndSaveLineageTasks(
                hdfsPath = request.hdfsPath,
                config = request.config ?: HdfsProcessingConfig(),
                taskCreatedBy = request.createdBy,
                batchId = request.batchId
            )
            
            logger.info("9a4e7f2c | HDFS完整处理完成: 处理{}个脚本，创建{}个LineageTask，更新{}个血缘", 
                       result.summary.totalScriptsProcessed,
                       result.summary.lineageTasksCreated,
                       result.summary.lineageDataUpdated)
            
            ResponseEntity.ok(result)
            
        } catch (e: Exception) {
            logger.error("c5f8e2a4 | HDFS完整处理发生异常", e)
            
            val errorResponse = HdfsFullProcessingResponse(
                success = false,
                message = "处理失败: ${e.message}",
                hdfsPath = request.hdfsPath,
                lineageTaskResults = emptyList(),
                lineageResults = emptyList(),
                summary = HdfsFullProcessingSummary()
            )
            
            ResponseEntity.status(500).body(errorResponse)
        }
    }

    /**
     * 健康检查：测试HDFS连接
     */
    @GetMapping("/health")
    fun healthCheck(): ResponseEntity<Map<String, Any>> {
        return try {
            val fileSystem = HdfsUtils.createHdfsConnection()
            fileSystem.close()
            
            ResponseEntity.ok(mapOf(
                "status" to "healthy",
                "hdfsConnection" to "ok",
                "timestamp" to System.currentTimeMillis()
            ) as Map<String, Any>)
        } catch (e: Exception) {
            logger.error("c7a2f9e4 | HDFS健康检查失败", e)
            
            ResponseEntity.status(503).body(mapOf(
                "status" to "unhealthy",
                "hdfsConnection" to "failed",
                "error" to (e.message ?: "Unknown error"),
                "timestamp" to System.currentTimeMillis()
            ))
        }
    }
}

/**
 * HDFS处理请求
 */
data class HdfsProcessingRequest(
    val hdfsPath: String,
    val config: HdfsProcessingConfig? = null,
    val processLineage: Boolean = true
)

/**
 * HDFS处理响应
 */
data class HdfsProcessingResponse(
    val success: Boolean,
    val message: String,
    val hdfsProcessingResult: HdfsShellScriptProcessingResult,
    val lineageResults: List<HdfsLineageProcessResult>,
    val summary: HdfsProcessingSummary
)

/**
 * HDFS处理摘要
 */
data class HdfsProcessingSummary(
    val totalScriptsFound: Int = 0,
    val successfulLineageProcessing: Int = 0,
    val lineageUpdated: Int = 0,
    val processingTimeMs: Long = 0
)

/**
 * HDFS完整处理请求（包含LineageTask保存）
 */
data class HdfsFullProcessingRequest(
    val hdfsPath: String,
    val config: HdfsProcessingConfig? = null,
    val createdBy: String? = null,
    val batchId: String? = null
)

/**
 * HDFS完整处理响应（包含LineageTask保存结果）
 */
data class HdfsFullProcessingResponse(
    val success: Boolean,
    val message: String,
    val hdfsPath: String,
    val lineageTaskResults: List<HdfsLineageTaskResult>,
    val lineageResults: List<HdfsLineageProcessResult>,
    val summary: HdfsFullProcessingSummary
)

/**
 * HDFS完整处理摘要
 */
data class HdfsFullProcessingSummary(
    val totalScriptsProcessed: Int = 0,
    val lineageTasksCreated: Int = 0,
    val lineageTasksUpdated: Int = 0,
    val lineageDataUpdated: Int = 0,
    val errorCount: Int = 0,
    val processingTimeMs: Long = 0
)

/**
 * HDFS脚本到LineageTask的转换结果
 */
data class HdfsLineageTaskResult(
    val scriptJob: HdfsShellScriptJob,
    val lineageTask: com.datayes.task.LineageTask?,
    val isNewTask: Boolean,
    val success: Boolean,
    val errorMessage: String? = null
)