package com.datayes.task

import com.datayes.dataexchange.*
import com.datayes.lineage.ProcessingResult
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.util.*

/**
 * 血缘任务服务 (Lineage Task Service)
 *
 * 实现血缘任务的核心业务逻辑，包括批量处理、重跑和查询功能
 * 遵循函数式核心，命令式外壳的架构原则
 */
@Service
@Transactional
class LineageTaskService(
    private val lineageTaskRepository: LineageTaskRepository,
    private val lineageTaskCustomRepository: LineageTaskCustomRepository,
    private val enhancedDataExchangeJobService: DataExchangeJobService,
    private val dataExchangeJobRepository: DataExchangeJobRepository
) {

    private val logger = LoggerFactory.getLogger(LineageTaskService::class.java)

    /**
     * 批量处理所有活跃作业的血缘 (Process all active jobs lineage)
     */
    fun processAllActiveJobs(request: BatchProcessRequest): BatchProcessResult {
        val startTime = System.currentTimeMillis()
        val batchId = request.batchId ?: generateBatchId()

        logger.info("01173f | 开始批量处理血缘任务，批次ID: $batchId")

        try {
            // 1. 获取所有活跃的数据交换作业
            val activeJobs = dataExchangeJobRepository.listActiveDataExchangeJobs()
            logger.info("fcfdaa | 获取到 ${activeJobs.size} 个活跃的数据交换作业")

            // 创建活跃作业的键集合，用于后续检查
            val activeJobKeys = activeJobs.map { "${it.readerJobId}_${it.writeJobId}" }.toSet()

            // 2. 处理每个作业的血缘
            val processedTasks = mutableListOf<TaskProcessResult>()
            var successCount = 0
            var failedCount = 0
            var unchangedCount = 0
            var updatedCount = 0
            var deactivatedCount = 0

            for (job in activeJobs) {
                try {
                    val taskResult = processJobLineageWithTask(job, request.executedBy, batchId)
                    processedTasks.add(taskResult)

                    when (taskResult.processingResult) {
                        ProcessingResult.UPDATED -> {
                            successCount++
                            updatedCount++
                        }

                        ProcessingResult.NO_CHANGE -> {
                            successCount++
                            unchangedCount++
                        }

                        ProcessingResult.FAILED -> failedCount++
                    }

                } catch (e: Exception) {
                    logger.error("ebf263 | 处理作业血缘时发生异常: ${job.readerJobId}_${job.writeJobId}", e)
                    failedCount++
                    processedTasks.add(createFailedTaskResult(job, e))
                }
            }

            // 3. 处理不再活跃的作业对应的血缘任务
            val existingTasks = lineageTaskRepository.findAllByTaskTypeAndIsEnabled(
                TaskType.DATA_EXCHANGE_PLATFORM,
                true
            )

            for (task in existingTasks) {
                // 如果任务的jobKey不在活跃作业列表中，则停用该任务
                if (task.jobKey != null && !activeJobKeys.contains(task.jobKey)) {
                    try {
                        logger.info("a8f23c | 停用不再活跃的血缘任务: id=${task.id}, jobKey=${task.jobKey}")

                        // 更新任务为停用状态
                        lineageTaskCustomRepository.updateTaskStatus(
                            taskId = task.id,
                            isEnabled = false,
                            updatedBy = request.executedBy ?: "system",  // 这个参数会被忽略，但保留以便将来可能的表结构变更
                            reason = "对应的数据交换作业不再活跃"
                        )

                        deactivatedCount++

                        // 添加到处理结果中
                        processedTasks.add(
                            TaskProcessResult(
                                taskId = task.id,
                                jobKey = task.jobKey,
                                taskName = task.taskName,
                                status = TaskStatus.DISABLED,
                                processingTimeMs = 0,
                                hasChanges = true,
                                processingResult = ProcessingResult.UPDATED,
                                errorMessage = null
                            )
                        )

                    } catch (e: Exception) {
                        logger.error("c4e1d9 | 停用不再活跃的血缘任务时发生异常: id=${task.id}", e)
                    }
                }
            }

            val totalProcessingTime = System.currentTimeMillis() - startTime

            logger.info(
                "5d81d671 | 批量处理完成: 总计=${activeJobs.size}, 成功=$successCount, 失败=$failedCount, " +
                        "更新=$updatedCount, 无变更=$unchangedCount, 停用=$deactivatedCount, 耗时=${totalProcessingTime}ms"
            )

            return BatchProcessResult(
                batchId = batchId,
                totalJobs = activeJobs.size,
                processedTasks = processedTasks,
                summary = ProcessingSummary(
                    successful = successCount,
                    failed = failedCount,
                    unchanged = unchangedCount,
                    updated = updatedCount,
                    deactivated = deactivatedCount
                ),
                processingTimeMs = totalProcessingTime
            )

        } catch (e: Exception) {
            logger.error("7d69158f | 批量处理血缘任务时发生致命错误", e)
            throw LineageTaskProcessingException("批量处理失败", e)
        }
    }

    /**
     * 处理单个作业的血缘并创建/更新任务 (Process single job lineage with task management)
     */
    private fun processJobLineageWithTask(
        job: DataExchangeJob,
        executedBy: String?,
        batchId: String
    ): TaskProcessResult {
        val jobKey = "${job.readerJobId}_${job.writeJobId}"
        val startTime = System.currentTimeMillis()

        // 1. 查找或创建血缘任务
        val task = findOrCreateLineageTask(job, jobKey, executedBy, batchId)

        // 2. 更新任务状态为执行中
        updateTaskStatus(task.id, TaskStatus.RUNNING, LocalDateTime.now())

        try {
            // 3. 执行血缘处理
            val lineageResult = enhancedDataExchangeJobService.processJobLineageWithChangeDetection(job, task.id)
            val processingTime = System.currentTimeMillis() - startTime

            // 4. 更新任务执行结果
            val executionId = UUID.randomUUID().toString()
            updateTaskExecutionResult(
                taskId = task.id,
                lineageResult = lineageResult,
                processingTime = processingTime,
                executionId = executionId
            )

            return TaskProcessResult(
                taskId = task.id,
                jobKey = jobKey,
                taskName = task.taskName,
                status = TaskStatus.SUCCESS,
                processingTimeMs = processingTime,
                hasChanges = lineageResult.hasChanges,
                processingResult = lineageResult.processingResult
            )

        } catch (e: Exception) {
            // 5. 处理失败，更新任务状态
            val processingTime = System.currentTimeMillis() - startTime
            updateTaskStatus(
                taskId = task.id,
                status = TaskStatus.FAILED,
                completedAt = LocalDateTime.now(),
                errorMessage = e.message,
                processingTimeMs = processingTime
            )

            return TaskProcessResult(
                taskId = task.id,
                jobKey = jobKey,
                taskName = task.taskName,
                status = TaskStatus.FAILED,
                processingTimeMs = processingTime,
                hasChanges = false,
                processingResult = ProcessingResult.FAILED,
                errorMessage = e.message
            )
        }
    }

    /**
     * 查找或创建血缘任务 (Find or create lineage task)
     */
    private fun findOrCreateLineageTask(
        job: DataExchangeJob,
        jobKey: String,
        executedBy: String?,
        batchId: String
    ): LineageTask {
        return lineageTaskRepository.findByJobKey(jobKey) ?: run {
            val taskName = "数据同步任务-${job.readerJobName}到${job.writeJobName}"
            val request = CreateLineageTaskRequest(
                taskName = taskName,
                taskType = TaskType.DATA_EXCHANGE_PLATFORM,
                sourceType = SourceType.DATA_EXCHANGE_JOB,
                sourceIdentifier = jobKey,
                sourceContent = job.readerSql,
                jobKey = jobKey,
                createdBy = executedBy,
                batchId = batchId
            )
            lineageTaskRepository.save(request.toLineageTask())
        }
    }

    /**
     * 重跑血缘任务 (Rerun lineage task)
     */
    fun rerunTask(taskId: Long, request: RerunTaskRequest): TaskExecutionResult {
        logger.info("61812a | 开始重跑血缘任务: $taskId")

        // 1. 获取任务信息
        val task = lineageTaskRepository.findById(taskId)
            .orElseThrow { LineageTaskNotFoundException("任务不存在: $taskId") }

        // 2. 检查任务状态
        if (task.taskStatus == TaskStatus.RUNNING) {
            throw IllegalStateException("任务正在执行中，无法重跑: $taskId")
        }

        if (task.taskStatus == TaskStatus.DISABLED) {
            throw IllegalStateException("任务已禁用，无法重跑: $taskId")
        }

        // 3. 获取对应的数据交换作业
        val jobKeyParts = task.jobKey?.split("_") ?: throw IllegalStateException("任务缺少作业键: $taskId")
        if (jobKeyParts.size < 2) {
            throw IllegalStateException("作业键格式错误: ${task.jobKey}")
        }

        val job = dataExchangeJobRepository.findDataExchangeJobByIds(jobKeyParts[0], jobKeyParts[1])
            ?: throw IllegalStateException("找不到对应的数据交换作业: ${task.jobKey}")

        // 4. 执行重跑
        val executionId = UUID.randomUUID().toString()
        updateTaskStatus(task.id, TaskStatus.RUNNING, LocalDateTime.now())

        try {
            val startTime = System.currentTimeMillis()
            val lineageResult = enhancedDataExchangeJobService.processJobLineageWithChangeDetection(job, task.id)
            val processingTime = System.currentTimeMillis() - startTime

            updateTaskExecutionResult(
                taskId = task.id,
                lineageResult = lineageResult,
                processingTime = processingTime,
                executionId = executionId
            )

            logger.info("09e066 | 血缘任务重跑成功: $taskId, 执行ID: $executionId")

            return TaskExecutionResult(
                taskId = taskId,
                executionId = executionId,
                status = TaskStatus.SUCCESS,
                message = "任务重跑成功"
            )

        } catch (e: Exception) {
            val processingTime = System.currentTimeMillis() - System.currentTimeMillis()
            updateTaskStatus(
                taskId = task.id,
                status = TaskStatus.FAILED,
                completedAt = LocalDateTime.now(),
                errorMessage = e.message,
                processingTimeMs = processingTime
            )

            logger.error("73a73b | 血缘任务重跑失败: $taskId", e)

            return TaskExecutionResult(
                taskId = taskId,
                executionId = executionId,
                status = TaskStatus.FAILED,
                message = "任务重跑失败: ${e.message}"
            )
        }
    }

    /**
     * 分页查询血缘任务 (Find tasks with pagination)
     */
    fun findTasks(criteria: LineageTaskCustomRepository.TaskQueryCriteria, pageable: Pageable): Page<LineageTask> {
        return lineageTaskCustomRepository.findTasksWithCriteria(criteria, pageable)
    }

    /**
     * 更新任务状态 (Update task status)
     */
    private fun updateTaskStatus(
        taskId: Long,
        status: TaskStatus,
        executedAt: LocalDateTime? = null,
        completedAt: LocalDateTime? = null,
        errorMessage: String? = null,
        processingTimeMs: Long? = null
    ) {
        lineageTaskCustomRepository.updateTaskExecution(
            taskId = taskId,
            status = status,
            executedAt = executedAt,
            completedAt = completedAt,
            processingTimeMs = processingTimeMs,
            hasChanges = null,
            errorMessage = errorMessage,
            executionId = null
        )
    }

    /**
     * 更新任务执行结果 (Update task execution result)
     */
    private fun updateTaskExecutionResult(
        taskId: Long,
        lineageResult: LineageProcessResult,
        processingTime: Long,
        executionId: String
    ) {
        val status = if (lineageResult.lineageResult.success) TaskStatus.SUCCESS else TaskStatus.FAILED
        val errorMessage = if (!lineageResult.lineageResult.success) {
            lineageResult.lineageResult.errors.joinToString("; ")
        } else null

        lineageTaskCustomRepository.updateTaskExecution(
            taskId = taskId,
            status = status,
            executedAt = null,
            completedAt = LocalDateTime.now(),
            processingTimeMs = processingTime,
            hasChanges = lineageResult.hasChanges,
            errorMessage = errorMessage,
            executionId = executionId
        )
    }

    /**
     * 生成批次ID (Generate batch ID)
     */
    private fun generateBatchId(): String {
        return "batch_${System.currentTimeMillis()}"
    }

    /**
     * 创建失败的任务结果 (Create failed task result)
     */
    private fun createFailedTaskResult(job: DataExchangeJob, exception: Exception): TaskProcessResult {
        return TaskProcessResult(
            taskId = 0,
            jobKey = "${job.readerJobId}_${job.writeJobId}",
            taskName = "数据同步任务-${job.readerJobName}到${job.writeJobName}",
            status = TaskStatus.FAILED,
            processingTimeMs = 0,
            hasChanges = false,
            processingResult = ProcessingResult.FAILED,
            errorMessage = exception.message
        )
    }
}

/**
 * 批量处理请求 (Batch Process Request)
 */
data class BatchProcessRequest(
    val executedBy: String? = null,
    val batchId: String? = null
)

/**
 * 批量处理结果 (Batch Process Result)
 */
data class BatchProcessResult(
    val batchId: String,
    val totalJobs: Int,
    val processedTasks: List<TaskProcessResult>,
    val summary: ProcessingSummary,
    val processingTimeMs: Long
)

/**
 * 任务处理结果 (Task Process Result)
 */
data class TaskProcessResult(
    val taskId: Long,
    val jobKey: String,
    val taskName: String,
    val status: TaskStatus,
    val processingTimeMs: Long,
    val hasChanges: Boolean,
    val processingResult: ProcessingResult,
    val errorMessage: String? = null
)

/**
 * 处理统计信息 (Processing Summary)
 */
data class ProcessingSummary(
    val successful: Int,
    val failed: Int,
    val unchanged: Int,
    val updated: Int,
    val deactivated: Int
)

/**
 * 重跑任务请求 (Rerun Task Request)
 */
data class RerunTaskRequest(
    val executedBy: String,
    val reason: String? = null
)

/**
 * 任务执行结果 (Task Execution Result)
 */
data class TaskExecutionResult(
    val taskId: Long,
    val executionId: String,
    val status: TaskStatus,
    val message: String
)

/**
 * 血缘任务处理异常 (Lineage Task Processing Exception)
 */
class LineageTaskProcessingException(message: String, cause: Throwable? = null) : Exception(message, cause)

/**
 * 血缘任务未找到异常 (Lineage Task Not Found Exception)
 */
class LineageTaskNotFoundException(message: String) : Exception(message) 