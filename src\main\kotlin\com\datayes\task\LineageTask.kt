package com.datayes.task

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.LocalDateTime

/**
 * 血缘任务实体 (Lineage Task Entity)
 *
 * 使用 Spring Data JDBC 映射到 lineage_tasks 表
 */
@Table("lineage_tasks")
data class LineageTask(

    @Id
    val id: Long = 0,

    @Column("task_name")
    val taskName: String,

    @Column("task_type")
    val taskType: TaskType,

    @Column("source_type")
    val sourceType: SourceType,

    @Column("source_identifier")
    val sourceIdentifier: String? = null,

    @Column("source_content")
    val sourceContent: String? = null,

    @Column("task_status")
    val taskStatus: TaskStatus = TaskStatus.PENDING,

    @Column("schedule_type")
    val scheduleType: ScheduleType = ScheduleType.MANUAL,

    @Column("cron_expression")
    val cronExpression: String? = null,

    @Column("is_enabled")
    val isEnabled: Boolean = true,

    @Column("created_by")
    val createdBy: String? = null,

    @Column("executed_at")
    val executedAt: LocalDateTime? = null,

    @Column("completed_at")
    val completedAt: LocalDateTime? = null,

    @Column("error_message")
    val errorMessage: String? = null,

    @Column("created_at")
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column("updated_at")
    val updatedAt: LocalDateTime = LocalDateTime.now(),

    // 新增字段 (New Fields for Task Management)
    @Column("job_key")
    val jobKey: String? = null,

    @Column("processing_time_ms")
    val processingTimeMs: Long? = null,

    @Column("has_changes")
    val hasChanges: Boolean = false,

    @Column("batch_id")
    val batchId: String? = null,

    @Column("execution_count")
    val executionCount: Int = 1,

    @Column("last_execution_id")
    val lastExecutionId: String? = null
)

/**
 * 任务类型枚举 (Task Type Enum)
 */
enum class TaskType {
    DATA_EXCHANGE_PLATFORM,  // 数据交换平台
    BASH_SCRIPT,            // Bash脚本
    MANUAL_IMPORT,          // 手动导入
    EXCEL_IMPORT            // Excel导入
}

/**
 * 血缘来源类型枚举 (Source Type Enum)
 */
enum class SourceType {
    DATA_EXCHANGE_JOB,      // 数据交换作业
    SCRIPT_ANALYSIS,        // 脚本分析
    MANUAL_INPUT            // 手动输入
}

/**
 * 任务状态枚举 (Task Status Enum)
 */
enum class TaskStatus {
    PENDING,    // 待执行
    RUNNING,    // 执行中
    SUCCESS,    // 执行成功
    FAILED,     // 执行失败
    CANCELLED,  // 已取消
    DISABLED    // 已禁用
}

/**
 * 调度类型枚举 (Schedule Type Enum)
 */
enum class ScheduleType {
    MANUAL,     // 手动执行
    SCHEDULED,  // 定时调度
    REAL_TIME   // 实时执行
}

/**
 * 血缘任务创建请求 (Lineage Task Creation Request)
 */
data class CreateLineageTaskRequest(
    val taskName: String,
    val taskType: TaskType,
    val sourceType: SourceType,
    val sourceIdentifier: String? = null,
    val sourceContent: String? = null,
    val jobKey: String? = null,
    val createdBy: String? = null,
    val batchId: String? = null
) {
    fun toLineageTask(): LineageTask {
        return LineageTask(
            taskName = taskName,
            taskType = taskType,
            sourceType = sourceType,
            sourceIdentifier = sourceIdentifier,
            sourceContent = sourceContent,
            jobKey = jobKey,
            createdBy = createdBy,
            batchId = batchId
        )
    }
}

/**
 * 血缘任务更新请求 (Lineage Task Update Request)
 */
data class UpdateLineageTaskRequest(
    val taskStatus: TaskStatus? = null,
    val executedAt: LocalDateTime? = null,
    val completedAt: LocalDateTime? = null,
    val errorMessage: String? = null,
    val processingTimeMs: Long? = null,
    val hasChanges: Boolean? = null,
    val lastExecutionId: String? = null
) 