<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    
    <property name="LOG_PATTERN" value="%d{HH:mm:ss.SSS} [%thread] [%X{requestId}] %-5level %logger{36} [%file:%line] - %msg%n"/>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>
    
    <!-- 内存日志 Appender，将日志存储到 InMemoryLogStore -->
<!--    <appender name="MEMORY" class="com.datayes.utils.InMemoryLogAppender">-->
<!--        &lt;!&ndash; 设置调用者数据，这样可以获取到方法名和行号 &ndash;&gt;-->
<!--        <includeCallerData>true</includeCallerData>-->
<!--    </appender>-->

    <logger name="com.datayes" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
<!--        <appender-ref ref="MEMORY"/>-->
    </logger>

    <root level="WARN">
        <appender-ref ref="CONSOLE"/>
<!--        <appender-ref ref="MEMORY"/>-->
    </root>
</configuration>