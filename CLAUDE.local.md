- For every log message, prepend a unique short literal uuid followed by ' | ', e.g. '3610991a | <the actual message>', ensuring that each unique prefix is only used for one distinct log message to maintain traceability.
- Prioritize data-oriented programming, focusing first on data structures and their transformations.
- Adopt a functional core and imperative shell architecture: encapsulate business logic in pure functions and handle side effects with an imperative shell to improve code testability and maintainability.
- Prefer simple and clear solutions, avoiding unnecessary complexity.
- Only introduce abstraction when it significantly improves code quality; avoid over-abstraction.
- Write inherently testable code, minimizing reliance on mocks.
- Prefer immutable objects and data structures to enhance code robustness.
- Don't try to run tests yourself; I want to run them manually when I want to.
- In <PERSON><PERSON><PERSON>'s triple-quoted strings (NOT normal string), replace every `$` that should be output literally with `${'$'}`.
