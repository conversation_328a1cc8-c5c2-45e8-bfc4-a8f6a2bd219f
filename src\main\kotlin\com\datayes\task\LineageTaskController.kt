package com.datayes.task

import com.datayes.ApiResponse
import com.datayes.lineage.LineageService
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import java.time.LocalDate

/**
 * 血缘任务管理控制器 (Lineage Task Management Controller)
 *
 * 提供血缘任务的 REST API 接口，包括批量处理、查询和重跑功能
 */
@RestController
@RequestMapping("/api/v1/lineage/tasks")
@CrossOrigin(origins = ["*"])
class LineageTaskController(private val lineageTaskService: LineageTaskService) {

    private val logger = LoggerFactory.getLogger(LineageTaskController::class.java)

    /**
     * 批量处理所有活跃作业的血缘 (Process all active jobs lineage)
     *
     * @param request 批量处理请求
     * @return 批量处理结果
     */
    @PostMapping("/process-all")
    fun processAllActiveTasks(@RequestBody request: BatchProcessRequest): ResponseEntity<ApiResponse<BatchProcessResult>> {
        return try {
            logger.info("19f9bc71 | 接收到批量处理血缘任务请求: executedBy=${request.executedBy}")

            val result = lineageTaskService.processAllActiveJobs(request)

            logger.info("989005e5 | 批量处理完成: batchId=${result.batchId}, 总任务数=${result.totalJobs}")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = result,
                    message = "批量处理完成"
                )
            )

        } catch (e: LineageTaskProcessingException) {
            logger.error("5e7c3c2b | 批量处理血缘任务失败", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("批量处理失败: ${e.message}"))

        } catch (e: Exception) {
            logger.error("a7fb962f | 批量处理血缘任务时发生未知错误", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统错误: ${e.message}"))
        }
    }

    /**
     * 分页查询血缘任务 (Get tasks with pagination and filtering)
     *
     * @param page 页码，默认0
     * @param size 页大小，默认20，最大100
     * @param status 任务状态过滤
     * @param taskType 任务类型过滤
     * @param taskName 任务名称模糊搜索
     * @param createdBy 创建人过滤
     * @param dateFrom 创建时间起始
     * @param dateTo 创建时间结束
     * @param sort 排序字段和方向，格式：field,direction
     * @return 分页查询结果
     */
    @GetMapping
    fun getTasks(
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "20") size: Int,
        @RequestParam(required = false) status: TaskStatus?,
        @RequestParam(required = false) taskType: TaskType?,
        @RequestParam(required = false) taskName: String?,
        @RequestParam(required = false) createdBy: String?,
        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") dateFrom: LocalDate?,
        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") dateTo: LocalDate?,
        @RequestParam(required = false) isEnabled: Boolean?,
        @RequestParam(required = false) batchId: String?,
        @RequestParam(defaultValue = "createdAt,desc") sort: String
    ): ResponseEntity<ApiResponse<Page<LineageTaskDto>>> {
        return try {
            // 验证参数
            val validatedSize = if (size > 100) 100 else size
            val validatedPage = if (page < 0) 0 else page

            // 构建查询条件
            val criteria = LineageTaskCustomRepository.TaskQueryCriteria(
                status = status,
                taskType = taskType,
                taskName = taskName,
                createdBy = createdBy,
                dateFrom = dateFrom,
                dateTo = dateTo,
                isEnabled = isEnabled,
                batchId = batchId
            )

            // 构建分页和排序
            val pageable = createPageable(validatedPage, validatedSize, sort)

            // 执行查询
            val result = lineageTaskService.findTasks(criteria, pageable)

            // 转换为DTO
            val dtoPage = result.map { it.toDto() }

            logger.debug("查询血缘任务: page=$page, size=$size, totalElements=${result.totalElements}")

            ResponseEntity.ok(ApiResponse.success(dtoPage))

        } catch (e: IllegalArgumentException) {
            logger.warn("查询参数错误", e)
            ResponseEntity.badRequest()
                .body(ApiResponse.error("查询参数错误: ${e.message}"))

        } catch (e: Exception) {
            logger.error("查询血缘任务时发生错误", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询失败: ${e.message}"))
        }
    }

    /**
     * 重跑血缘任务 (Rerun lineage task)
     *
     * @param taskId 任务ID
     * @param request 重跑请求
     * @return 任务执行结果
     */
    @PostMapping("/{taskId}/rerun")
    fun rerunTask(
        @PathVariable taskId: Long,
        @RequestBody request: RerunTaskRequest
    ): ResponseEntity<ApiResponse<TaskExecutionResult>> {
        return try {
            logger.info("2e4f19ba | 接收到重跑血缘任务请求: taskId=$taskId, executedBy=${request.executedBy}")

            val result = lineageTaskService.rerunTask(taskId, request)

            logger.info("血缘任务重跑: taskId=$taskId, status=${result.status}")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = result,
                    message = result.message
                )
            )

        } catch (e: LineageTaskNotFoundException) {
            logger.warn("任务不存在: taskId=$taskId", e)
            ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error("任务不存在: $taskId"))

        } catch (e: IllegalStateException) {
            logger.warn("任务状态不允许重跑: taskId=$taskId", e)
            ResponseEntity.status(HttpStatus.CONFLICT)
                .body(ApiResponse.error("任务状态不允许重跑: ${e.message}"))

        } catch (e: Exception) {
            logger.error("重跑血缘任务时发生错误: taskId=$taskId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("重跑失败: ${e.message}"))
        }
    }

    /**
     * 获取任务详情 (Get task details)
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    @GetMapping("/{taskId}")
    fun getTask(@PathVariable taskId: Long): ResponseEntity<ApiResponse<LineageTaskDto>> {
        return try {
            // 这里需要在 LineageTaskService 中添加 findById 方法
            // val task = lineageTaskService.findById(taskId)
            //     ?: return ResponseEntity.notFound().build()

            // ResponseEntity.ok(ApiResponse.success(task.toDto()))

            // 临时实现，返回未实现错误
            ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED)
                .body(ApiResponse.error("功能待实现"))

        } catch (e: Exception) {
            logger.error("获取任务详情时发生错误: taskId=$taskId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取任务详情失败: ${e.message}"))
        }
    }


    
    /**
     * 创建分页对象 (Create pageable object)
     */
    private fun createPageable(page: Int, size: Int, sortParam: String): PageRequest {
        return try {
            val sortParts = sortParam.split(",")
            if (sortParts.size == 2) {
                val property = sortParts[0].trim()
                val direction = Sort.Direction.fromString(sortParts[1].trim())
                PageRequest.of(page, size, Sort.by(direction, property))
            } else {
                PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"))
            }
        } catch (e: Exception) {
            logger.warn("解析排序参数失败，使用默认排序: $sortParam", e)
            PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"))
        }
    }
}

/**
 * 血缘任务 DTO (Lineage Task DTO)
 */
data class LineageTaskDto(
    val id: Long,
    val taskName: String,
    val taskType: TaskType,
    val sourceType: SourceType,
    val sourceIdentifier: String?,
    val status: TaskStatus,
    val scheduleType: ScheduleType,
    val isEnabled: Boolean,
    val createdBy: String?,
    val executedAt: String?,
    val completedAt: String?,
    val processingTimeMs: Long?,
    val hasChanges: Boolean,
    val batchId: String?,
    val executionCount: Int,
    val errorMessage: String?,
    val createdAt: String,
    val updatedAt: String
)

/**
 * LineageTask 转 DTO 扩展函数 (Extension function to convert LineageTask to DTO)
 */
fun LineageTask.toDto(): LineageTaskDto {
    return LineageTaskDto(
        id = this.id,
        taskName = this.taskName,
        taskType = this.taskType,
        sourceType = this.sourceType,
        sourceIdentifier = this.sourceIdentifier,
        status = this.taskStatus,
        scheduleType = this.scheduleType,
        isEnabled = this.isEnabled,
        createdBy = this.createdBy,
        executedAt = this.executedAt?.toString(),
        completedAt = this.completedAt?.toString(),
        processingTimeMs = this.processingTimeMs,
        hasChanges = this.hasChanges,
        batchId = this.batchId,
        executionCount = this.executionCount,
        errorMessage = this.errorMessage,
        createdAt = this.createdAt.toString(),
        updatedAt = this.updatedAt.toString()
    )
}

