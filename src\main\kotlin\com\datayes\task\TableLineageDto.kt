package com.datayes.task

import com.datayes.lineage.ColumnLineageView
import com.datayes.lineage.TableLineageView
import java.math.BigDecimal

/**
 * 表血缘关系 DTO (Table Lineage DTO)
 * 
 * 包含表的上游和下游血缘关系信息
 */
data class TableLineageDto(
    val tableId: Long,
    val upstream: List<TableRelationshipDto> = emptyList(),
    val downstream: List<TableRelationshipDto> = emptyList()
)

/**
 * 表关系 DTO (Table Relationship DTO)
 * 
 * 表示两个表之间的血缘关系
 */
data class TableRelationshipDto(
    val sourceTableId: Long,
    val sourceTable: String,
    val sourceSchema: String?,
    val sourceDatasource: String,
    val targetTableId: Long,
    val targetTable: String,
    val targetSchema: String?,
    val targetDatasource: String,
    val lineageType: String?,
    val level: Int,
    val columnMappings: List<ColumnMappingDto> = emptyList()
)

/**
 * 列映射 DTO (Column Mapping DTO)
 * 
 * 表示源表列和目标表列之间的映射关系
 */
data class ColumnMappingDto(
    val sourceColumn: String,
    val sourceDataType: String,
    val targetColumn: String,
    val targetDataType: String,
    val transformationType: String?,
    val transformationDescription: String?,
    val transformationExpression: String?,
    val confidenceScore: BigDecimal?
)

/**
 * 将 TableLineageView 列表转换为 TableRelationshipDto 列表
 */
fun List<TableLineageView>.toTableRelationshipDtos(): List<TableRelationshipDto> {
    return this.map { view ->
        TableRelationshipDto(
            sourceTableId = view.sourceTableId,
            sourceTable = view.sourceTable,
            sourceSchema = view.sourceSchema,
            sourceDatasource = view.sourceDatasource,
            targetTableId = view.targetTableId,
            targetTable = view.targetTable,
            targetSchema = view.targetSchema,
            targetDatasource = view.targetDatasource,
            lineageType = view.lineageType,
            level = view.level,
            columnMappings = emptyList() // 初始为空，后续填充
        )
    }
}

/**
 * 将 ColumnLineageView 转换为 ColumnMappingDto
 */
fun ColumnLineageView.toColumnMappingDto(): ColumnMappingDto {
    return ColumnMappingDto(
        sourceColumn = this.sourceColumn,
        sourceDataType = this.sourceDataType,
        targetColumn = this.targetColumn,
        targetDataType = this.targetDataType,
        transformationType = this.transformationType,
        transformationDescription = this.transformationDescription,
        transformationExpression = this.transformationExpression,
        confidenceScore = this.confidenceScore
    )
}
