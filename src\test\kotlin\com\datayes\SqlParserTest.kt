package com.datayes

import com.datayes.sql.SqlParser
import com.datayes.sql.SqlParsingException
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName

class SqlParserTest {

    @Test
    @DisplayName("Should parse a simple SELECT query correctly")
    fun testParseSimpleSelectQuery() {
        // Given
        val sqlQuery = """
            SELECT id, name, age FROM users
        """.trimIndent()

        // When
        val result = SqlParser.parse(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify tables
        assertThat(result.tables).hasSize(1)
        assertThat(result.tables[0].name).isEqualTo("users")
        assertThat(result.tables[0].schema).isNull()
        assertThat(result.tables[0].alias).isNull()

        // Verify columns
        assertThat(result.columns).hasSize(3)
        assertThat(result.columns).extracting("name").containsExactly("id", "name", "age")
        assertThat(result.columns).extracting("tablePrefix").containsOnlyNulls()
        assertThat(result.columns).extracting("alias").containsOnlyNulls()
        assertThat(result.columns).extracting("isWildcard").containsOnly(false)
    }

    @Test
    @DisplayName("Should parse a query with schema, table alias and column alias")
    fun testParseQueryWithSchemaAndAliases() {
        // Given
        val sqlQuery = """
            SELECT u.id AS user_id, u.name AS user_name 
            FROM schema1.users u
        """.trimIndent()

        // When
        val result = SqlParser.parse(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify tables
        assertThat(result.tables).hasSize(1)
        assertThat(result.tables[0].name).isEqualTo("users")
        assertThat(result.tables[0].schema).isEqualTo("schema1")
        assertThat(result.tables[0].alias).isEqualTo("u")

        // Verify columns
        assertThat(result.columns).hasSize(2)

        // First column
        assertThat(result.columns[0].name).isEqualTo("id")
        assertThat(result.columns[0].tablePrefix).isEqualTo("u")
        assertThat(result.columns[0].alias).isEqualTo("user_id")
        assertThat(result.columns[0].originalExpression).isEqualTo("u.id")
        assertThat(result.columns[0].isWildcard).isFalse()

        // Second column
        assertThat(result.columns[1].name).isEqualTo("name")
        assertThat(result.columns[1].tablePrefix).isEqualTo("u")
        assertThat(result.columns[1].alias).isEqualTo("user_name")
        assertThat(result.columns[1].originalExpression).isEqualTo("u.name")
        assertThat(result.columns[1].isWildcard).isFalse()
    }

    @Test
    @DisplayName("Should parse a query with JOIN clauses")
    fun testParseQueryWithJoins() {
        // Given
        val sqlQuery = """
            SELECT u.id, o.order_id, o.total
            FROM users u
            JOIN orders o ON u.id = o.user_id
        """.trimIndent()

        // When
        val result = SqlParser.parse(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify tables
        assertThat(result.tables).hasSize(2)

        // First table
        assertThat(result.tables[0].name).isEqualTo("users")
        assertThat(result.tables[0].alias).isEqualTo("u")

        // Second table
        assertThat(result.tables[1].name).isEqualTo("orders")
        assertThat(result.tables[1].alias).isEqualTo("o")

        // Verify columns
        assertThat(result.columns).hasSize(3)
        assertThat(result.columns).extracting("name").containsExactly("id", "order_id", "total")
        assertThat(result.columns).extracting("tablePrefix").containsExactly("u", "o", "o")
    }

    @Test
    @DisplayName("Should parse a query with wildcard columns")
    fun testParseQueryWithWildcards() {
        // Given
        val sqlQuery = """
            SELECT * FROM users
        """.trimIndent()

        // When
        val result = SqlParser.parse(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify tables
        assertThat(result.tables).hasSize(1)
        assertThat(result.tables[0].name).isEqualTo("users")

        // Verify columns
        assertThat(result.columns).hasSize(1)
        assertThat(result.columns[0].name).isEqualTo("*")
        assertThat(result.columns[0].isWildcard).isTrue()
        assertThat(result.columns[0].tablePrefix).isNull()
    }

    @Test
    @DisplayName("Should parse a query with table-qualified wildcard columns")
    fun testParseQueryWithTableWildcards() {
        // Given
        val sqlQuery = """
            SELECT u.*, o.order_id
            FROM users u
            JOIN orders o ON u.id = o.user_id
        """.trimIndent()

        // When
        val result = SqlParser.parse(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify tables
        assertThat(result.tables).hasSize(2)

        // Verify columns
        assertThat(result.columns).hasSize(2)

        // First column (wildcard)
        assertThat(result.columns[0].name).isEqualTo("*")
        assertThat(result.columns[0].tablePrefix).isEqualTo("u")
        assertThat(result.columns[0].isWildcard).isTrue()
        assertThat(result.columns[0].originalExpression).isEqualTo("u.*")

        // Second column
        assertThat(result.columns[1].name).isEqualTo("order_id")
        assertThat(result.columns[1].tablePrefix).isEqualTo("o")
        assertThat(result.columns[1].isWildcard).isFalse()
    }

    @Test
    @DisplayName("Should parse a query with subquery")
    fun testParseQueryWithSubquery() {
        // Given
        val sqlQuery = """
            SELECT u.id, s.total
            FROM users u
            JOIN (SELECT user_id, SUM(amount) as total FROM orders GROUP BY user_id) s
            ON u.id = s.user_id
        """.trimIndent()

        // When
        val result = SqlParser.parse(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify tables - should extract tables from subqueries in JOIN clauses
        assertThat(result.tables).hasSize(2)
        assertThat(result.tables).extracting("name").containsExactlyInAnyOrder("users", "orders")
        assertThat(result.tables).filteredOn { it.name == "users" }
            .extracting("alias")
            .contains("u")

        // Verify columns from the main query
        assertThat(result.columns).hasSize(2)
        assertThat(result.columns).extracting("name").containsExactly("id", "total")
    }

    @Test
    @DisplayName("Should throw SqlParsingException for invalid SQL")
    fun testParseInvalidSql() {
        // Given
        val invalidSql = "SELECT * FREM users" // Intentional typo in FROM

        // When/Then
        assertThatThrownBy { SqlParser.parse(invalidSql) }
            .isInstanceOf(SqlParsingException::class.java)
            .hasMessageContaining("SQL parsing error")
    }

    @Test
    @DisplayName("Should parse the example SQL from the main method")
    fun testParseExampleSql() {
        // Given - using the example from the main method
        val sqlQuery = """
            SELECT a.id, b.name AS user_name, COUNT(*) as total_count
            FROM schema1.users a
            JOIN orders b ON a.id = b.user_id
            LEFT JOIN (SELECT * FROM products WHERE active = 1) p ON b.product_id = p.id
            WHERE a.status = 'active'
            AND b.created_at > (SELECT MAX(created_at) FROM login_history WHERE user_id = a.id)
            GROUP BY a.id, b.name
            ORDER BY total_count DESC
            LIMIT 10
        """.trimIndent()

        // When
        val result = SqlParser.parse(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify tables - should extract tables from main query and subqueries
        assertThat(result.tables).hasSize(3)
        assertThat(result.tables).extracting("name").containsExactlyInAnyOrder("users", "orders", "products")

        // Verify schema for users table
        assertThat(result.tables).filteredOn { it.name == "users" }
            .extracting("schema")
            .contains("schema1")

        // Verify aliases
        assertThat(result.tables).filteredOn { it.name == "users" }
            .extracting("alias")
            .contains("a")

        assertThat(result.tables).filteredOn { it.name == "orders" }
            .extracting("alias")
            .contains("b")

        // Verify columns
        assertThat(result.columns).hasSize(3)

        // First column
        assertThat(result.columns[0].name).isEqualTo("id")
        assertThat(result.columns[0].tablePrefix).isEqualTo("a")

        // Second column
        assertThat(result.columns[1].name).isEqualTo("name")
        assertThat(result.columns[1].tablePrefix).isEqualTo("b")
        assertThat(result.columns[1].alias).isEqualTo("user_name")

        // Third column (aggregate function)
        assertThat(result.columns[2].name).isEqualTo("COUNT(*)")
        assertThat(result.columns[2].alias).isEqualTo("total_count")
    }

    @Test
    @DisplayName("Should parse INSERT OVERWRITE with UNION ALL query")
    fun testParseInsertOverwriteWithUnionAll() {
        // Given
        val sqlQuery = """
            insert overwrite table urp_dws.full_lcget
            select GrpAppNo,
                        GrpPolicyNo,
                        GrpProductNo,
                        AppNo,
                        PolicyNo,
                        ProductNo,
                        LiabilityCode,
                        GetLiabilityCode,
                        PayNo,
                        StatusCgDate,
                        ReportNo,
                        ClmRegisterNo,
                        ClaimNo,
                        CaseNo,
                        EdorAcceptNo,
                        EdorNo,
                        EdorType,
                        DivYear,
                        TempfeeNo,
                        ActuGetNo,
                        CompanyCode,
                        LiabilityName,
                        GPFlag,
                        GetLiabilityName,
                        InitialPremium,
                        CurrentPremium,
                        AccumPremium,
                        InitialAmnt,
                        EffectiveAmnt,
                        RiskAmnt,
                        AllowanceType,
                        DailyAllowanceAmnt,
                        effdate,
                        invaliddate,
                        Status,
                        WaitingPeriod,
                        DeductibleType,
                        Deductible,
                        ClaimRatio,
                        ManageCom,
                        insuredgroupcode,
                        groupname,
                        SystemCode,
                        ListImportDate,
                        ClaimBackDate,
                        MakeDate,
                        MakeTime,
                        ModifyDate,
                        ModifyTime,
                   last_dw_opt_date
              from (select GrpAppNo,
                        GrpPolicyNo,
                        GrpProductNo,
                        AppNo,
                        PolicyNo,
                        ProductNo,
                        LiabilityCode,
                        GetLiabilityCode,
                        PayNo,
                        StatusCgDate,
                        ReportNo,
                        ClmRegisterNo,
                        ClaimNo,
                        CaseNo,
                        EdorAcceptNo,
                        EdorNo,
                        EdorType,
                        DivYear,
                        TempfeeNo,
                        ActuGetNo,
                        CompanyCode,
                        LiabilityName,
                        GPFlag,
                        GetLiabilityName,
                        InitialPremium,
                        CurrentPremium,
                        AccumPremium,
                        InitialAmnt,
                        EffectiveAmnt,
                        RiskAmnt,
                        AllowanceType,
                        DailyAllowanceAmnt,
                        effdate,
                        invaliddate,
                        Status,
                        WaitingPeriod,
                        DeductibleType,
                        Deductible,
                        ClaimRatio,
                        ManageCom,
                        insuredgroupcode,
                        groupname,
                        SystemCode,
                        ListImportDate,
                        ClaimBackDate,
                        MakeDate,
                        MakeTime,
                        ModifyDate,
                        ModifyTime,
                           last_dw_opt_date
                      from urp_dws.pre_full_lcget f
                     where not exists (select 1 
                                         from urp_dws.pre_increment_lcget i 
                                        where i.ProductNo = f.ProductNo
                                        and i.LiabilityCode =f.LiabilityCode
                                        and i.GetLiabilityCode =f.GetLiabilityCode and nvl(i.rdbms_opt_type,'') <> 'DELETE')
                    union all 
                    select GrpAppNo,
                        GrpPolicyNo,
                        GrpProductNo,
                        AppNo,
                        PolicyNo,
                        ProductNo,
                        LiabilityCode,
                        GetLiabilityCode,
                        PayNo,
                        StatusCgDate,
                        ReportNo,
                        ClmRegisterNo,
                        ClaimNo,
                        CaseNo,
                        EdorAcceptNo,
                        EdorNo,
                        EdorType,
                        DivYear,
                        TempfeeNo,
                        ActuGetNo,
                        CompanyCode,
                        LiabilityName,
                        GPFlag,
                        GetLiabilityName,
                        InitialPremium,
                        CurrentPremium,
                        AccumPremium,
                        InitialAmnt,
                        EffectiveAmnt,
                        RiskAmnt,
                        AllowanceType,
                        DailyAllowanceAmnt,
                        effdate,
                        invaliddate,
                        Status,
                        WaitingPeriod,
                        DeductibleType,
                        Deductible,
                        ClaimRatio,
                        ManageCom,
                        insuredgroupcode,
                        groupname,
                        SystemCode,
                        ListImportDate,
                        ClaimBackDate,
                        MakeDate,
                        MakeTime,
                        ModifyDate,
                        ModifyTime,
                          '${'$'}{`date -d "${'$'}1 +1 days" "+%Y-%m-%d"`}' as last_dw_opt_date
                      from urp_dws.pre_increment_lcget i ) m;
        """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify target table
        assertThat(result.targetTable.name).isEqualTo("full_lcget")
        assertThat(result.targetTable.schema).isEqualTo("urp_dws")
        assertThat(result.targetTable.alias).isNull()

        // Verify no explicit target columns (INSERT INTO table SELECT ... without column list)
        assertThat(result.targetColumns).isNull()

        // Verify source tables - should extract tables from SELECT part
        assertThat(result.sourceTables).hasSize(2)
        assertThat(result.sourceTables).extracting("name").containsExactlyInAnyOrder("pre_full_lcget", "pre_increment_lcget")

        // Verify schema for source tables
        assertThat(result.sourceTables).filteredOn { it.name == "pre_full_lcget" }
            .extracting("schema")
            .contains("urp_dws")

        assertThat(result.sourceTables).filteredOn { it.name == "pre_increment_lcget" }
            .extracting("schema")
            .contains("urp_dws")

        // Verify table aliases  
        assertThat(result.sourceTables).filteredOn { it.name == "pre_full_lcget" }
            .extracting("alias")
            .contains("f")

        assertThat(result.sourceTables).filteredOn { it.name == "pre_increment_lcget" }
            .extracting("alias")
            .contains("i")

        // Verify source columns - UNION ALL extracts columns from both sides, so we get more than the outer SELECT
        assertThat(result.sourceColumns).hasSizeGreaterThanOrEqualTo(48)
        
        // Check some key columns
        assertThat(result.sourceColumns).extracting("name").contains("GrpAppNo", "ProductNo", "LiabilityCode", "last_dw_opt_date")

        // Verify column mappings - should match the source columns count
        assertThat(result.columnMappings).hasSize(result.sourceColumns.size)
        
        // Check first column mapping
        assertThat(result.columnMappings[0].sourceColumn.name).isEqualTo("GrpAppNo")
        assertThat(result.columnMappings[0].targetColumnName).isEqualTo("GrpAppNo")
        assertThat(result.columnMappings[0].targetColumnIndex).isEqualTo(0)

        // Check that last_dw_opt_date is in the mappings
        val lastDwOptMappings = result.columnMappings.filter { it.sourceColumn.name == "last_dw_opt_date" }
        assertThat(lastDwOptMappings).isNotEmpty()
    }

}
