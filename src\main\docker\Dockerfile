FROM registry.minshenglife.com/infrastructure/openjdk:17.0.2-slim-bullseyearm

# 设置时区 (无外网无法安装 tzdata，直接设置 JVM 时区)
ENV TZ=Asia/Shanghai
#RUN apt-get update && apt-get install -y tzdata && \
#    ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 添加主机名解析
RUN { \
    echo "*********** tbds-10-9-112-26"; \
    echo "*********** tbds-10-9-112-25"; \
    echo "*********** tbds-10-9-112-28"; \
    echo "*********** tbds-10-9-112-27"; \
    echo "*********** tbds-10-9-112-32"; \
    echo "*********** tbds-10-9-112-31"; \
    echo "*********** tbds-10-9-112-30"; \
    echo "*********** tbds-10-9-112-29"; \
    } >> /etc/hosts && \
    echo "--- [DEBUG] Dockerfile RUN: /etc/hosts 内容如下: ---" && \
    cat /etc/hosts && \
    echo "--- [DEBUG] Dockerfile RUN: /etc/hosts 内容结束 ---"

# 设置工作目录
WORKDIR /app

# 添加应用JAR包
ADD target/dgp-lineage-collector-0.0.1-SNAPSHOT.jar /app/dgp-lineage-collector-0.0.1-SNAPSHOT.jar

# 暴露应用端口
EXPOSE 9502

# 设置容器启动命令
ENTRYPOINT ["java", "-XX:+UseG1GC", "-Xmx1536m", "-Xms512m", "-XX:+ExplicitGCInvokesConcurrent", "-XX:+ParallelRefProcEnabled", "-XX:MaxGCPauseMillis=200", "-XX:+DisableExplicitGC", "-XX:+HeapDumpOnOutOfMemoryError", "-XX:HeapDumpPath=/app/heapdump.hprof", "-Djava.security.egd=file:/dev/./urandom", "-Duser.timezone=Asia/Shanghai", "-jar", "/app/dgp-lineage-collector-0.0.1-SNAPSHOT.jar"]
