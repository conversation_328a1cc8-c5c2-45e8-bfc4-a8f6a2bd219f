package com.datayes.hdfs

import com.datayes.lineage.JobProcessingHistory
import com.datayes.lineage.JobProcessingHistoryRepository
import com.datayes.lineage.JobType
import com.datayes.lineage.ProcessingResult
import com.datayes.lineage.DataLineage
import com.datayes.lineage.LineageChangeDetectionService
import com.datayes.lineage.LineageRepository
import com.datayes.lineage.LineageResult
import com.datayes.task.LineageTask
import com.datayes.task.LineageTaskRepository
import com.datayes.task.TaskType
import com.datayes.task.SourceType
import com.datayes.task.TaskStatus
import com.datayes.task.ScheduleType
import org.apache.hadoop.fs.Path
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.util.UUID

/**
 * HDFS Shell Script 服务层
 * 
 * 提供HDFS shell脚本的发现、提取、处理和血缘分析功能
 * 类似于DataExchangeJobService，但专门处理HDFS中的shell脚本
 */
@Service
class HdfsShellScriptService(
    private val lineageRepository: LineageRepository,
    private val changeDetectionService: LineageChangeDetectionService,
    private val processingHistoryRepository: JobProcessingHistoryRepository,
    private val lineageTaskRepository: LineageTaskRepository
) {
    
    private val logger = LoggerFactory.getLogger(HdfsShellScriptService::class.java)
    
    /**
     * 处理指定HDFS路径下的所有shell脚本
     * 
     * @param hdfsPath HDFS路径
     * @param config 处理配置
     * @return 处理结果
     */
    fun processHdfsShellScripts(
        hdfsPath: String,
        config: HdfsProcessingConfig = HdfsProcessingConfig()
    ): HdfsShellScriptProcessingResult {
        val startTime = System.currentTimeMillis()
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()
        
        return try {
            logger.info("5a3e8f2d | 开始处理HDFS路径: {}", hdfsPath)
            
            // 1. 连接HDFS
            val fileSystem = HdfsUtils.createHdfsConnection()
            val path = Path(hdfsPath)
            
            // 2. 验证路径存在
            if (!HdfsUtils.pathExists(fileSystem, path)) {
                errors.add("HDFS路径不存在: $hdfsPath")
                return HdfsShellScriptProcessingResult(
                    jobs = emptyList(),
                    hdfsPath = hdfsPath,
                    errors = errors,
                    warnings = warnings
                )
            }
            
            // 3. 处理ZIP文件并提取shell脚本
            val zipProcessingResult = HdfsUtils.processZipFilesForShellScripts(fileSystem, path)
            logger.info("b7c4e9f1 | 处理了{}个ZIP文件，发现{}个shell脚本", 
                       zipProcessingResult.totalZipFilesProcessed, 
                       zipProcessingResult.totalShellScriptsFound)
            
            // 4. 转换为HdfsShellScriptJob对象
            val jobs = mutableListOf<HdfsShellScriptJob>()
            
            for (zipResult in zipProcessingResult.results) {
                for (shellScript in zipResult.shellScripts) {
                    // 检查脚本大小限制
                    if (shellScript.sizeBytes > config.maxScriptSizeBytes) {
                        warnings.add("脚本文件过大，跳过处理: ${shellScript.name} (${shellScript.sizeBytes} bytes)")
                        continue
                    }
                    
                    val job = HdfsShellScriptJob.fromShellScript(zipResult.zipFilePath, shellScript)
                    jobs.add(job)
                    
                    logger.debug("c6d2a8e4 | 创建作业: {} from {}", job.jobId, shellScript.name)
                }
            }
            
            // 5. 关闭HDFS连接
            fileSystem.close()
            
            val processingTime = System.currentTimeMillis() - startTime
            logger.info("9e5f3b7a | HDFS处理完成，耗时{}ms，共发现{}个shell脚本作业", processingTime, jobs.size)
            
            HdfsShellScriptProcessingResult(
                jobs = jobs,
                hdfsPath = hdfsPath,
                errors = errors,
                warnings = warnings
            )
            
        } catch (e: Exception) {
            logger.error("1a8d4f6e | 处理HDFS shell脚本时发生异常", e)
            errors.add("处理异常: ${e.message}")
            
            HdfsShellScriptProcessingResult(
                jobs = emptyList(),
                hdfsPath = hdfsPath,
                errors = errors,
                warnings = warnings
            )
        }
    }
    
    /**
     * 处理单个shell脚本作业的血缘分析（带变更检测）
     * 
     * @param job HDFS shell脚本作业
     * @param taskId 可选的任务ID
     * @return 血缘处理结果
     */
    fun processJobLineageWithChangeDetection(
        job: HdfsShellScriptJob, 
        taskId: Long? = null
    ): HdfsLineageProcessResult {
        val startTime = System.currentTimeMillis()
        val jobKey = job.jobId
        
        return try {
            logger.debug("f2e7c9b3 | 开始处理shell脚本血缘: {}", jobKey)
            
            // 1. 转换为血缘信息
            val lineageResult = HdfsShellScriptLineageConverter.convertToLineage(job)
            
            if (!lineageResult.success) {
                return createFailureResult(job, lineageResult, startTime, "血缘转换失败")
            }
            
            val dataLineage = lineageResult.lineage!!
            
            // 2. 检测变更
            val changeDetection = changeDetectionService.detectChanges(jobKey, dataLineage)
            
            val processingResult = if (changeDetection.hasChanges) {
                // 3a. 有变更：更新数据库
                logger.info("e8a4f2c6 | 检测到shell脚本血缘变更，更新数据库: {}", jobKey)
                updateHdfsLineageInDatabase(jobKey, dataLineage, job, taskId)
                HdfsProcessingResult.UPDATED
            } else {
                // 3b. 无变更：仅更新处理时间
                logger.debug("d3b6e9f5 | 未检测到shell脚本血缘变更，跳过数据库更新: {}", jobKey)
                HdfsProcessingResult.NO_CHANGE
            }
            
            val processingTime = System.currentTimeMillis() - startTime
            
            // 4. 记录处理历史（与DataExchangeJobService保持一致）
            recordHdfsProcessingHistory(
                jobKey = jobKey,
                job = job,
                result = processingResult,
                lineageHash = changeDetection.currentHash,
                processingTime = processingTime
            )
            
            HdfsLineageProcessResult(
                job = job,
                lineageResult = lineageResult,
                processingTimeMs = processingTime,
                hasChanges = changeDetection.hasChanges,
                processingResult = processingResult
            )
            
        } catch (e: Exception) {
            logger.error("a7e3d8f2 | 处理shell脚本血缘时发生异常: {}", jobKey, e)
            createFailureResult(job, null, startTime, "处理异常: ${e.message}")
        }
    }
    
    /**
     * 批量处理多个shell脚本作业的血缘分析
     * 
     * @param jobs shell脚本作业列表
     * @return 血缘处理结果列表
     */
    fun processMultipleJobsLineage(jobs: List<HdfsShellScriptJob>): List<HdfsLineageProcessResult> {
        logger.info("c5f8e2a9 | 开始批量处理{}个shell脚本作业的血缘", jobs.size)
        
        val results = jobs.map { job ->
            processJobLineageWithChangeDetection(job)
        }
        
        val successCount = results.count { it.processingResult != HdfsProcessingResult.FAILED }
        val updateCount = results.count { it.processingResult == HdfsProcessingResult.UPDATED }
        
        logger.info("b9d4e7f3 | 批量处理完成：成功{}个，更新{}个，失败{}个", 
                   successCount, updateCount, results.size - successCount)
        
        return results
    }
    
    /**
     * 完整的HDFS路径处理工作流：
     * 1. 获取HDFS路径下所有非备份ZIP文件
     * 2. 解析每个ZIP中的shell脚本为DataLineage
     * 3. 将每个脚本映射为lineage_tasks表中的一行并保存
     * 4. 保存DataLineage血缘数据
     * 
     * @param hdfsPath HDFS路径
     * @param config 处理配置
     * @param taskCreatedBy 任务创建者
     * @param batchId 批处理ID
     * @return 完整的处理结果
     */
    fun processHdfsPathAndSaveLineageTasks(
        hdfsPath: String,
        config: HdfsProcessingConfig = HdfsProcessingConfig(),
        taskCreatedBy: String? = null,
        batchId: String? = null
    ): HdfsFullProcessingResponse {
        val startTime = System.currentTimeMillis()
        val actualBatchId = batchId ?: "hdfs_batch_${UUID.randomUUID().toString().take(8)}"
        
        logger.info("e7f2a9c4 | 开始完整HDFS处理工作流: path={}, batchId={}", hdfsPath, actualBatchId)
        
        return try {
            // 1. 处理HDFS shell脚本
            val hdfsProcessingResult = processHdfsShellScripts(hdfsPath, config)
            
            if (hdfsProcessingResult.errors.isNotEmpty()) {
                return createErrorResponse(hdfsPath, hdfsProcessingResult.errors, startTime)
            }
            
            val jobs = hdfsProcessingResult.jobs
            logger.info("b3c8e5a7 | 发现{}个shell脚本作业", jobs.size)
            
            // 2. 为每个脚本创建或更新LineageTask
            val lineageTaskResults = jobs.map { job ->
                createOrUpdateLineageTask(job, taskCreatedBy, actualBatchId)
            }
            
            // 3. 处理血缘分析（只对成功创建LineageTask的作业）
            val successfulTasks = lineageTaskResults.filter { it.success }
            val lineageResults = successfulTasks.map { taskResult ->
                processJobLineageWithTask(taskResult.scriptJob, taskResult.lineageTask!!.id)
            }
            
            val processingTime = System.currentTimeMillis() - startTime
            
            // 4. 构建响应
            val summary = HdfsFullProcessingSummary(
                totalScriptsProcessed = jobs.size,
                lineageTasksCreated = lineageTaskResults.count { it.isNewTask && it.success },
                lineageTasksUpdated = lineageTaskResults.count { !it.isNewTask && it.success },
                lineageDataUpdated = lineageResults.count { it.processingResult == HdfsProcessingResult.UPDATED },
                errorCount = lineageTaskResults.count { !it.success } + lineageResults.count { it.processingResult == HdfsProcessingResult.FAILED },
                processingTimeMs = processingTime
            )
            
            logger.info("a9d6f4c2 | HDFS完整处理工作流完成: 脚本{}个，任务创建{}个，任务更新{}个，血缘更新{}个，耗时{}ms",
                       summary.totalScriptsProcessed,
                       summary.lineageTasksCreated,
                       summary.lineageTasksUpdated,
                       summary.lineageDataUpdated,
                       processingTime)
            
            HdfsFullProcessingResponse(
                success = summary.errorCount == 0,
                message = if (summary.errorCount == 0) {
                    "成功处理${summary.totalScriptsProcessed}个脚本，创建${summary.lineageTasksCreated}个任务，更新${summary.lineageTasksUpdated}个任务"
                } else {
                    "处理完成但有${summary.errorCount}个错误"
                },
                hdfsPath = hdfsPath,
                lineageTaskResults = lineageTaskResults,
                lineageResults = lineageResults,
                summary = summary
            )
            
        } catch (e: Exception) {
            logger.error("f5c2e8a3 | HDFS完整处理工作流发生异常", e)
            createErrorResponse(hdfsPath, listOf("系统异常: ${e.message}"), startTime)
        }
    }

    /**
     * 查询指定HDFS目录下的所有ZIP文件
     * 
     * @param hdfsPath HDFS目录路径
     * @param includeBackups 是否包含备份文件（带时间戳的文件）
     * @return ZIP文件查询结果
     */
    fun queryZipFiles(hdfsPath: String, includeBackups: Boolean = false): HdfsZipFileQueryResult {
        val startTime = System.currentTimeMillis()
        
        return try {
            logger.info("e4a7b8f2 | 开始查询HDFS ZIP文件: {}", hdfsPath)
            
            // 1. 连接HDFS
            val fileSystem = HdfsUtils.createHdfsConnection()
            val path = Path(hdfsPath)
            
            // 2. 验证路径存在
            if (!HdfsUtils.pathExists(fileSystem, path)) {
                return HdfsZipFileQueryResult(
                    success = false,
                    message = "HDFS路径不存在: $hdfsPath",
                    hdfsPath = hdfsPath,
                    zipFiles = emptyList(),
                    queryTimeMs = System.currentTimeMillis() - startTime
                )
            }
            
            // 3. 查找所有ZIP文件
            val allZipFiles = HdfsUtils.findZipFiles(fileSystem, path)
            logger.debug("f2c6e9a4 | 发现{}个ZIP文件", allZipFiles.size)
            
            // 4. 根据配置过滤备份文件
            val filteredZipFiles = if (includeBackups) {
                allZipFiles
            } else {
                HdfsUtils.filterBackupZipFiles(allZipFiles)
            }
            
            // 5. 转换为详细信息
            val zipFileInfos = filteredZipFiles.map { zipFilePath ->
                val fileName = zipFilePath.substringAfterLast("/")
                val isBackup = isBackupFile(fileName)
                
                HdfsZipFileInfo(
                    filePath = zipFilePath,
                    fileName = fileName,
                    isBackup = isBackup
                )
            }
            
            // 6. 关闭HDFS连接
            fileSystem.close()
            
            val queryTime = System.currentTimeMillis() - startTime
            logger.info("9c3a5e7b | ZIP文件查询完成，耗时{}ms，找到{}个文件（总共{}个）", 
                       queryTime, filteredZipFiles.size, allZipFiles.size)
            
            HdfsZipFileQueryResult(
                success = true,
                message = "成功查询到${filteredZipFiles.size}个ZIP文件",
                hdfsPath = hdfsPath,
                zipFiles = zipFileInfos,
                totalFound = allZipFiles.size,
                totalAfterFilter = filteredZipFiles.size,
                queryTimeMs = queryTime
            )
            
        } catch (e: Exception) {
            logger.error("b6d8f4a9 | 查询HDFS ZIP文件时发生异常", e)
            
            HdfsZipFileQueryResult(
                success = false,
                message = "查询失败: ${e.message}",
                hdfsPath = hdfsPath,
                zipFiles = emptyList(),
                queryTimeMs = System.currentTimeMillis() - startTime
            )
        }
    }

    /**
     * 获取活动的shell脚本作业（模拟数据库查询）
     * 注意：这里是占位符实现，实际应该连接数据库
     */
    fun getActiveShellScriptJobs(): List<HdfsShellScriptJob> {
        // TODO: 实现从数据库查询活动的shell脚本作业
        logger.warn("6e2a9f4c | getActiveShellScriptJobs() 尚未实现数据库查询")
        return emptyList()
    }
    
    /**
     * 为HdfsShellScriptJob创建或更新LineageTask
     * 
     * @param job HDFS shell脚本作业
     * @param createdBy 任务创建者
     * @param batchId 批处理ID
     * @return LineageTask创建/更新结果
     */
    private fun createOrUpdateLineageTask(
        job: HdfsShellScriptJob,
        createdBy: String?,
        batchId: String
    ): HdfsLineageTaskResult {
        return try {
            logger.debug("c7e4f9a2 | 开始为脚本{}创建或更新LineageTask", job.jobId)
            
            // 检查是否已存在同样的任务
            val existingTask = lineageTaskRepository.findByJobKey(job.jobId)
            
            val lineageTask = if (existingTask != null) {
                // 更新现有任务
                logger.debug("d8f5e3a7 | 发现现有LineageTask，更新: {}", existingTask.id)
                updateExistingLineageTask(existingTask, job, batchId)
            } else {
                // 创建新任务
                logger.debug("a4c7e2f9 | 创建新的LineageTask for {}", job.jobId)
                createNewLineageTask(job, createdBy, batchId)
            }
            
            HdfsLineageTaskResult(
                scriptJob = job,
                lineageTask = lineageTask,
                isNewTask = existingTask == null,
                success = true
            )
            
        } catch (e: Exception) {
            logger.error("b6f9c4e3 | 创建或更新LineageTask失败: {}", job.jobId, e)
            HdfsLineageTaskResult(
                scriptJob = job,
                lineageTask = null,
                isNewTask = false,
                success = false,
                errorMessage = "创建任务失败: ${e.message}"
            )
        }
    }
    
    /**
     * 创建新的LineageTask
     */
    private fun createNewLineageTask(
        job: HdfsShellScriptJob,
        createdBy: String?,
        batchId: String
    ): LineageTask {
        val taskName = "HDFS脚本任务-${job.jobName}"
        
        val newTask = LineageTask(
            taskName = taskName,
            taskType = TaskType.BASH_SCRIPT,
            sourceType = SourceType.SCRIPT_ANALYSIS,
            sourceIdentifier = job.jobId,
            sourceContent = job.scriptContent,
            taskStatus = TaskStatus.PENDING,
            scheduleType = ScheduleType.MANUAL,
            isEnabled = true,
            createdBy = createdBy,
            jobKey = job.jobId,
            batchId = batchId,
            executionCount = 1,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
        
        return lineageTaskRepository.save(newTask)
    }
    
    /**
     * 更新现有的LineageTask
     */
    private fun updateExistingLineageTask(
        existingTask: LineageTask,
        job: HdfsShellScriptJob,
        batchId: String
    ): LineageTask {
        val updatedTask = existingTask.copy(
            sourceContent = job.scriptContent, // 更新脚本内容
            batchId = batchId,
            executionCount = existingTask.executionCount + 1,
            updatedAt = LocalDateTime.now()
        )
        
        return lineageTaskRepository.save(updatedTask)
    }
    
    /**
     * 处理作业血缘分析（带LineageTask ID）
     */
    private fun processJobLineageWithTask(
        job: HdfsShellScriptJob,
        taskId: Long
    ): HdfsLineageProcessResult {
        return processJobLineageWithChangeDetection(job, taskId)
    }
    
    /**
     * 创建错误响应
     */
    private fun createErrorResponse(
        hdfsPath: String,
        errors: List<String>,
        startTime: Long
    ): HdfsFullProcessingResponse {
        return HdfsFullProcessingResponse(
            success = false,
            message = "处理失败: ${errors.firstOrNull() ?: "未知错误"}",
            hdfsPath = hdfsPath,
            lineageTaskResults = emptyList(),
            lineageResults = emptyList(),
            summary = HdfsFullProcessingSummary(
                errorCount = errors.size,
                processingTimeMs = System.currentTimeMillis() - startTime
            )
        )
    }

    /**
     * 更新HDFS shell脚本血缘信息到数据库
     * 临时实现，直接调用现有的血缘存储逻辑
     */
    private fun updateHdfsLineageInDatabase(
        jobKey: String, 
        dataLineage: DataLineage, 
        job: HdfsShellScriptJob, 
        taskId: Long?
    ) {
        try {
            // 1. 标记旧的血缘关系为非活跃
            lineageRepository.deactivateLineageByJobKey(jobKey)
            
            // 2. 保存新的血缘信息
            val relationshipIds = lineageRepository.saveDataLineage(dataLineage, taskId, jobKey)
            
            // 3. 更新引用计数
            lineageRepository.updateReferenceCounts(dataLineage)
            
            logger.debug("f8e4c2a9 | 成功更新HDFS血缘到数据库，关系ID: {}", relationshipIds)
            
        } catch (e: Exception) {
            logger.error("a3f7e5b8 | 更新HDFS血缘到数据库失败: {}", jobKey, e)
            throw e
        }
    }
    
    /**
     * 记录HDFS shell脚本处理历史
     * 使用JobProcessingHistory表存储HDFS作业的处理记录
     */
    private fun recordHdfsProcessingHistory(
        jobKey: String,
        job: HdfsShellScriptJob,
        result: HdfsProcessingResult,
        lineageHash: String,
        processingTime: Long
    ) {
        try {
            // 将HDFS处理结果转换为标准格式
            val processingResult = when (result) {
                HdfsProcessingResult.UPDATED -> ProcessingResult.UPDATED
                HdfsProcessingResult.NO_CHANGE -> ProcessingResult.NO_CHANGE
                HdfsProcessingResult.FAILED -> ProcessingResult.FAILED
            }
            
            val history = JobProcessingHistory(
                jobKey = jobKey,
                jobType = JobType.HDFS_SHELL_SCRIPT,
                readerJobId = null, // HDFS作业不使用这些字段
                writeJobId = null,  // HDFS作业不使用这些字段
                processingResult = processingResult,
                changesDetected = result == HdfsProcessingResult.UPDATED,
                processingDurationMs = processingTime,
                lineageHash = lineageHash
            )
            
            processingHistoryRepository.save(history)
            logger.debug("a5e8f3c2 | 成功记录HDFS处理历史: {}", jobKey)
            
        } catch (e: Exception) {
            logger.error("d7c4f9e6 | 记录HDFS处理历史失败: {}", jobKey, e)
            // 不抛出异常，避免影响主流程
        }
    }
    
    private fun createFailureResult(
        job: HdfsShellScriptJob,
        lineageResult: LineageResult?,
        startTime: Long,
        errorMessage: String
    ): HdfsLineageProcessResult {
        val failureResult = lineageResult ?: LineageResult(
            lineage = null,
            warnings = emptyList(),
            errors = listOf(errorMessage),
            success = false
        )
        
        return HdfsLineageProcessResult(
            job = job,
            lineageResult = failureResult,
            processingTimeMs = System.currentTimeMillis() - startTime,
            hasChanges = false,
            processingResult = HdfsProcessingResult.FAILED
        )
    }
    
    /**
     * 检查文件名是否为备份文件（包含时间戳）
     */
    private fun isBackupFile(fileName: String): Boolean {
        val lowerFileName = fileName.lowercase()
        val backupPatterns = listOf(
            Regex(".*\\.\\d{14}\\.zip$"),  // filename.YYYYMMDDHHMMSS.zip
            Regex(".*\\d{14}\\.zip$")     // filenameYYYYMMDDHHMMSS.zip
        )
        return backupPatterns.any { pattern -> pattern.matches(lowerFileName) }
    }
}

/**
 * HDFS处理结果枚举
 */
enum class HdfsProcessingResult {
    NO_CHANGE,  // 无变更
    UPDATED,    // 已更新
    FAILED      // 处理失败
}

/**
 * HDFS血缘处理结果
 */
data class HdfsLineageProcessResult(
    val job: HdfsShellScriptJob,
    val lineageResult: LineageResult,
    val processingTimeMs: Long,
    val hasChanges: Boolean = false,
    val processingResult: HdfsProcessingResult = HdfsProcessingResult.NO_CHANGE
)

/**
 * HDFS ZIP文件信息
 */
data class HdfsZipFileInfo(
    val filePath: String,
    val fileName: String,
    val isBackup: Boolean = false
)

/**
 * HDFS ZIP文件查询结果
 */
data class HdfsZipFileQueryResult(
    val success: Boolean,
    val message: String,
    val hdfsPath: String,
    val zipFiles: List<HdfsZipFileInfo>,
    val totalFound: Int = zipFiles.size,
    val totalAfterFilter: Int = zipFiles.size,
    val queryTimeMs: Long = 0
)