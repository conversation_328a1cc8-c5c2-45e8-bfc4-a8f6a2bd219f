-- ====================================================================
-- DGP Lineage Management Database Schema
-- 血缘管理数据库模式设计
-- ====================================================================

-- 1. 系统管理表 (System Management)
-- 用于血缘目录中的系统分类展示
CREATE TABLE lineage_systems (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    system_name VARCHAR(100) NOT NULL UNIQUE COMMENT '系统名称',
    system_code VARCHAR(50) NOT NULL UNIQUE COMMENT '系统编码',
    description TEXT COMMENT '系统描述',
    contact_person VARCHAR(100) COMMENT '联系人',
    status ENUM('ACTIVE', 'INACTIVE') NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_system_code (system_code),
    INDEX idx_system_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统管理表';

-- 2. 数据源信息表 (Data Source Information)
-- 标准化数据库连接信息，避免重复存储
CREATE TABLE lineage_datasources (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    datasource_name VARCHAR(100) NOT NULL COMMENT '数据源名称',
    db_type VARCHAR(20) NOT NULL COMMENT '数据库类型 (mysql, postgresql, oracle, hive2等)',
    host VARCHAR(255) NOT NULL COMMENT '主机地址',
    port INT NOT NULL COMMENT '端口号',
    database_name VARCHAR(100) NOT NULL COMMENT '数据库名',
    connection_string TEXT NOT NULL COMMENT '原始连接字符串',
    system_id BIGINT COMMENT '所属系统ID',
    status ENUM('ACTIVE', 'INACTIVE') NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_datasource (db_type, host, port, database_name),
    INDEX idx_system_id (system_id),
    INDEX idx_db_type (db_type),
    INDEX idx_status (status),
    
    FOREIGN KEY (system_id) REFERENCES lineage_systems(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据源信息表';

-- 3. 表信息表 (Table Information)
-- 存储血缘关系中涉及的所有表
CREATE TABLE lineage_tables (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    datasource_id BIGINT NOT NULL COMMENT '数据源ID',
    schema_name VARCHAR(100) COMMENT '模式名',
    table_name VARCHAR(100) NOT NULL COMMENT '表名',
    table_type ENUM('TABLE', 'VIEW', 'TEMP_TABLE', 'EXTERNAL_TABLE') DEFAULT 'TABLE' COMMENT '表类型',
    chinese_name VARCHAR(200) COMMENT '中文名称',
    description TEXT COMMENT '表描述',
    sync_frequency VARCHAR(50) COMMENT '同步频率',
    requirement_id VARCHAR(100) COMMENT '软开需求编号',
    status ENUM('ACTIVE', 'INACTIVE') NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_table (datasource_id, schema_name, table_name),
    INDEX idx_table_name (table_name),
    INDEX idx_schema_name (schema_name),
    INDEX idx_chinese_name (chinese_name),
    INDEX idx_status (status)
    
--     FOREIGN KEY (datasource_id) REFERENCES lineage_datasources(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表信息表';

-- 4. 列信息表 (Column Information)
-- 存储血缘关系中涉及的所有列
CREATE TABLE lineage_columns (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    table_id BIGINT NOT NULL COMMENT '表ID',
    column_name VARCHAR(100) NOT NULL COMMENT '列名',
    data_type VARCHAR(50) NOT NULL COMMENT '数据类型',
    column_comment TEXT COMMENT '列注释',
    is_primary_key BOOLEAN DEFAULT FALSE COMMENT '是否主键',
    is_nullable BOOLEAN DEFAULT TRUE COMMENT '是否可为空',
    default_value VARCHAR(500) COMMENT '默认值',
    column_order INT COMMENT '列顺序',
    status ENUM('ACTIVE', 'INACTIVE') NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_column (table_id, column_name),
    INDEX idx_column_name (column_name),
    INDEX idx_data_type (data_type),
    INDEX idx_status (status)
    
--     FOREIGN KEY (table_id) REFERENCES lineage_tables(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='列信息表';

-- 5. 血缘任务表 (Lineage Tasks)
-- 记录血缘采集任务的信息
CREATE TABLE lineage_tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    task_type ENUM('DATA_EXCHANGE_PLATFORM', 'BASH_SCRIPT', 'MANUAL_IMPORT', 'EXCEL_IMPORT') NOT NULL COMMENT '任务类型',
    source_type ENUM('DATA_EXCHANGE_JOB', 'SCRIPT_ANALYSIS', 'MANUAL_INPUT') NOT NULL COMMENT '血缘来源类型',
    source_identifier VARCHAR(500) COMMENT '源标识符 (job_id, script_path等)',
    source_content LONGTEXT COMMENT '源内容 (原始SQL, 脚本内容等)',
    task_status ENUM('PENDING', 'RUNNING', 'SUCCESS', 'FAILED', 'CANCELLED') NOT NULL DEFAULT 'PENDING' COMMENT '任务状态',
    schedule_type ENUM('MANUAL', 'SCHEDULED', 'REAL_TIME') DEFAULT 'MANUAL' COMMENT '调度类型',
    cron_expression VARCHAR(100) COMMENT 'Cron表达式',
    is_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_by VARCHAR(100) COMMENT '创建人',
    executed_at TIMESTAMP NULL COMMENT '执行时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_task_type (task_type),
    INDEX idx_source_type (source_type),
    INDEX idx_task_status (task_status),
    INDEX idx_is_enabled (is_enabled),
    INDEX idx_executed_at (executed_at),
    INDEX idx_created_by (created_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='血缘任务表';

-- 6. 血缘关系表 (Lineage Relationships)
-- 存储表级和字段级血缘关系
CREATE TABLE lineage_relationships (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id BIGINT COMMENT '关联的血缘任务ID',
    relationship_type ENUM('TABLE_LEVEL', 'COLUMN_LEVEL') NOT NULL COMMENT '关系类型',
    source_table_id BIGINT NOT NULL COMMENT '源表ID',
    target_table_id BIGINT NOT NULL COMMENT '目标表ID',
    source_column_id BIGINT COMMENT '源列ID (仅列级血缘)',
    target_column_id BIGINT COMMENT '目标列ID (仅列级血缘)',
    lineage_type ENUM('DIRECT_COPY', 'SQL_QUERY', 'AGGREGATION', 'JOIN', 'FILTER', 'COMPLEX_TRANSFORMATION') DEFAULT 'DIRECT_COPY' COMMENT '血缘类型',
    transformation_type ENUM('NONE', 'TYPE_CAST', 'FUNCTION', 'EXPRESSION', 'CONSTANT', 'AGGREGATION', 'CONDITIONAL') DEFAULT 'NONE' COMMENT '转换类型',
    transformation_description TEXT COMMENT '转换描述',
    transformation_expression TEXT COMMENT '转换表达式',
    source_system ENUM('SYSTEM_COLLECTED', 'MANUAL_MAINTAINED') NOT NULL DEFAULT 'SYSTEM_COLLECTED' COMMENT '血缘来源',
    confidence_score DECIMAL(3,2) DEFAULT 1.0 COMMENT '置信度 (0-1)',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
    version INT DEFAULT 1 COMMENT '版本号',
    created_by VARCHAR(100) COMMENT '创建人',
    validated_by VARCHAR(100) COMMENT '验证人',
    validated_at TIMESTAMP NULL COMMENT '验证时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_task_id (task_id),
    INDEX idx_relationship_type (relationship_type),
    INDEX idx_source_table (source_table_id),
    INDEX idx_target_table (target_table_id),
    INDEX idx_source_column (source_column_id),
    INDEX idx_target_column (target_column_id),
    INDEX idx_lineage_type (lineage_type),
    INDEX idx_source_system (source_system),
    INDEX idx_is_active (is_active),
    INDEX idx_created_by (created_by),
    -- 复合索引用于血缘查询优化
    INDEX idx_lineage_query (source_table_id, target_table_id, relationship_type, is_active),
    INDEX idx_column_lineage (source_column_id, target_column_id, is_active)
    
--     FOREIGN KEY (task_id) REFERENCES lineage_tasks(id) ON DELETE SET NULL,
--     FOREIGN KEY (source_table_id) REFERENCES lineage_tables(id) ON DELETE CASCADE,
--     FOREIGN KEY (target_table_id) REFERENCES lineage_tables(id) ON DELETE CASCADE,
--     FOREIGN KEY (source_column_id) REFERENCES lineage_columns(id) ON DELETE CASCADE,
--     FOREIGN KEY (target_column_id) REFERENCES lineage_columns(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='血缘关系表';

-- 7. 血缘执行日志表 (Lineage Execution Logs)
-- 记录血缘采集的详细日志
CREATE TABLE lineage_execution_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id BIGINT NOT NULL COMMENT '任务ID',
    execution_id VARCHAR(100) NOT NULL COMMENT '执行ID',
    log_level ENUM('DEBUG', 'INFO', 'WARN', 'ERROR') NOT NULL COMMENT '日志级别',
    log_message TEXT NOT NULL COMMENT '日志消息',
    exception_stack TEXT COMMENT '异常堆栈',
    execution_step VARCHAR(100) COMMENT '执行步骤',
    processing_time_ms BIGINT COMMENT '处理时间(毫秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_task_id (task_id),
    INDEX idx_execution_id (execution_id),
    INDEX idx_log_level (log_level),
    INDEX idx_execution_step (execution_step),
    INDEX idx_created_at (created_at)
    
--     FOREIGN KEY (task_id) REFERENCES lineage_tasks(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='血缘执行日志表';

-- 8. 血缘统计信息表 (Lineage Statistics)
-- 用于血缘目录的统计展示
CREATE TABLE lineage_statistics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    system_id BIGINT COMMENT '系统ID',
    datasource_id BIGINT COMMENT '数据源ID',
    table_count INT DEFAULT 0 COMMENT '表数量',
    column_count INT DEFAULT 0 COMMENT '列数量',
    relationship_count INT DEFAULT 0 COMMENT '血缘关系数量',
    last_collection_time TIMESTAMP COMMENT '最后采集时间',
    health_status ENUM('HEALTHY', 'WARNING', 'ERROR') DEFAULT 'HEALTHY' COMMENT '健康状态',
    statistics_date DATE NOT NULL COMMENT '统计日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_stats (system_id, datasource_id, statistics_date),
    INDEX idx_statistics_date (statistics_date),
    INDEX idx_health_status (health_status)
    
--     FOREIGN KEY (system_id) REFERENCES lineage_systems(id) ON DELETE CASCADE,
--     FOREIGN KEY (datasource_id) REFERENCES lineage_datasources(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='血缘统计信息表';

-- 9. 上传脚本表 (Uploaded Scripts) - 用于脚本影响分析
CREATE TABLE uploaded_scripts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    script_name VARCHAR(200) NOT NULL COMMENT '脚本名称',
    script_type ENUM('SQL', 'SHELL') NOT NULL COMMENT '脚本类型',
    file_path VARCHAR(500) COMMENT '文件路径',
    file_size BIGINT COMMENT '文件大小(字节)',
    file_hash VARCHAR(64) COMMENT '文件哈希值',
    script_content LONGTEXT COMMENT '脚本内容',
    upload_user VARCHAR(100) NOT NULL COMMENT '上传用户',
    analysis_status ENUM('PENDING', 'ANALYZING', 'COMPLETED', 'FAILED') DEFAULT 'PENDING' COMMENT '分析状态',
    analysis_result LONGTEXT COMMENT '分析结果JSON',
    temporary_lineage_id VARCHAR(100) COMMENT '临时血缘关系ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_script_type (script_type),
    INDEX idx_upload_user (upload_user),
    INDEX idx_analysis_status (analysis_status),
    INDEX idx_created_at (created_at),
    INDEX idx_file_hash (file_hash)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='上传脚本表';

-- ====================================================================
-- 初始化数据 (Initial Data)
-- ====================================================================

-- 插入默认系统
INSERT INTO lineage_systems (system_name, system_code, description, status) VALUES 
('数据交互平台', 'DATA_EXCHANGE_PLATFORM', '内部数据交互平台系统', 'ACTIVE'),
('大数据平台', 'BIG_DATA_PLATFORM', '大数据处理平台', 'ACTIVE'),
('外部系统', 'EXTERNAL_SYSTEM', '外部第三方系统', 'ACTIVE'),
('未分类系统', 'UNCATEGORIZED', '未分类的数据源', 'ACTIVE');