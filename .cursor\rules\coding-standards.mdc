---
description:
globs:
alwaysApply: false
---
# 编码标准 (Coding Standards)

## 代码风格 (Code Style)

### 1. Kotlin 编码规范 (Kotlin Coding Conventions)
- 遵循 [Kotlin 官方编码规范](https://kotlinlang.org/docs/coding-conventions.html)
- 使用 4 个空格缩进 (4-space indentation)
- 类名使用 PascalCase，函数和变量使用 camelCase
- 常量使用 UPPER_SNAKE_CASE

### 2. 命名约定 (Naming Conventions)
- **类名** (Class Names): 描述性且有意义 (descriptive and meaningful)
  - `LineageTaskService`, `SqlParser`, `DataExchangeJob`
- **函数名** (Function Names): 动词开头，描述行为 (verb-first, describing behavior)
  - `parseLineage()`, `processTask()`, `calculateHash()`
- **变量名** (Variable Names): 清晰表达用途 (clearly express purpose)
  - `sourceTableId`, `targetColumnName`, `lineageHash`

### 3. 注释规范 (Comment Standards)
- 使用 KDoc 风格的文档注释 (KDoc-style documentation comments)
- 解释"为什么"而不是"什么" (explain "why" not "what")
- 包含中英文对照 (include Chinese-English bilingual descriptions)

```kotlin
/**
 * 计算血缘关系的哈希值 (Calculate hash value for lineage relationship)
 * 
 * 用于检测血缘关系是否发生变化，确保数据一致性
 * (Used to detect if lineage relationship has changed, ensuring data consistency)
 * 
 * @param lineage 血缘关系对象 (lineage relationship object)
 * @return 计算得到的哈希值 (calculated hash value)
 */
fun calculateLineageHash(lineage: Lineage): String
```

## 架构原则 (Architectural Principles)

### 1. 数据为中心编程 (Data-Oriented Programming)
- 优先定义数据结构 (prioritize data structure definition)
- 数据转换函数分离 (separate data transformation functions)
- 不可变数据结构 (immutable data structures)

### 2. 函数式核心，命令式外壳 (Functional Core, Imperative Shell)
- 纯函数业务逻辑 (pure function business logic)
- 副作用隔离在边界 (side effects isolated at boundaries)
- 易于测试的设计 (testable design)

### 3. 单一职责原则 (Single Responsibility Principle)
- 每个类只负责一个功能 (each class responsible for one function)
- 模块边界清晰 (clear module boundaries)
- 松耦合，高内聚 (loose coupling, high cohesion)

## 错误处理 (Error Handling)

### 1. 异常策略 (Exception Strategy)
- 使用领域特定异常 (use domain-specific exceptions)
- 提供有意义的错误信息 (provide meaningful error messages)
- 记录详细的错误上下文 (log detailed error context)

### 2. 结果类型 (Result Types)
```kotlin
sealed class ParseResult {
    data class Success(val lineage: Lineage) : ParseResult()
    data class Failure(val error: String, val cause: Throwable?) : ParseResult()
}
```

## 测试标准 (Testing Standards)

### 1. 测试结构 (Test Structure)
- 使用 AAA 模式：Arrange, Act, Assert
- 描述性测试名称 (descriptive test names)
- 独立的测试用例 (independent test cases)

### 2. 测试覆盖率 (Test Coverage)
- 核心业务逻辑 100% 覆盖 (100% coverage for core business logic)
- 边界条件测试 (boundary condition testing)
- 错误路径测试 (error path testing)

## 性能考虑 (Performance Considerations)

### 1. 数据库访问 (Database Access)
- 使用批量操作 (use batch operations)
- 避免 N+1 查询问题 (avoid N+1 query problems)
- 合理使用索引 (reasonable use of indexes)

### 2. 内存管理 (Memory Management)
- 及时释放资源 (timely resource release)
- 避免内存泄漏 (avoid memory leaks)
- 使用流式处理大数据集 (use streaming for large datasets)
