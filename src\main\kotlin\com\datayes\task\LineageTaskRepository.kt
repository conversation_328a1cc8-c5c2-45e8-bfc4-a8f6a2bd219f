package com.datayes.task

import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.data.jdbc.repository.query.Query
import org.springframework.data.repository.CrudRepository
import org.springframework.data.repository.PagingAndSortingRepository
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import org.springframework.stereotype.Repository
import java.sql.ResultSet
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * 血缘任务仓库接口 (Lineage Task Repository Interface)
 *
 * 基于 Spring Data JDBC 的血缘任务数据访问接口
 */
@Repository
interface LineageTaskRepository : CrudRepository<LineageTask, Long>, PagingAndSortingRepository<LineageTask, Long> {

    /**
     * 根据作业键查找任务 (Find task by job key)
     */
    @Query("SELECT * FROM lineage_tasks WHERE job_key = :jobKey")
    fun findByJobKey(jobKey: String): LineageTask?

    /**
     * 根据状态查找任务 (Find tasks by status)
     */
    @Query("SELECT * FROM lineage_tasks WHERE task_status IN (:statuses)")
    fun findByTaskStatusIn(statuses: List<TaskStatus>): List<LineageTask>

    /**
     * 查找指定批次的任务 (Find tasks by batch id)
     */
    @Query("SELECT * FROM lineage_tasks WHERE batch_id = :batchId")
    fun findByBatchId(batchId: String): List<LineageTask>

    /**
     * 查找活跃任务 (Find enabled tasks)
     */
    @Query("SELECT * FROM lineage_tasks WHERE is_enabled = true")
    fun findActiveTask(): List<LineageTask>
    
    /**
     * 根据任务类型和启用状态查找任务 (Find tasks by task type and enabled status)
     */
    @Query("SELECT * FROM lineage_tasks WHERE task_type = :taskType AND is_enabled = :isEnabled")
    fun findAllByTaskTypeAndIsEnabled(taskType: TaskType, isEnabled: Boolean): List<LineageTask>
}

/**
 * 血缘任务自定义仓库实现 (Custom Lineage Task Repository Implementation)
 *
 * 提供复杂查询和分页功能
 */
@Repository
class LineageTaskCustomRepository(private val jdbcTemplate: JdbcTemplate) {

    /**
     * 任务查询条件 (Task Query Criteria)
     */
    data class TaskQueryCriteria(
        val status: TaskStatus? = null,
        val taskType: TaskType? = null,
        val taskName: String? = null,
        val createdBy: String? = null,
        val dateFrom: LocalDate? = null,
        val dateTo: LocalDate? = null,
        val isEnabled: Boolean? = null,
        val batchId: String? = null
    )

    /**
     * 分页查询任务 (Find tasks with pagination and criteria)
     */
    fun findTasksWithCriteria(criteria: TaskQueryCriteria, pageable: Pageable): Page<LineageTask> {
        val whereClause = buildWhereClause(criteria)
        val orderClause = buildOrderClause(pageable)

        // 构建查询SQL
        val countSql = "SELECT COUNT(*) FROM lineage_tasks $whereClause"
        val dataSql = """
            SELECT * FROM lineage_tasks 
            $whereClause 
            $orderClause 
            LIMIT ${pageable.pageSize} OFFSET ${pageable.offset}
        """.trimIndent()

        // 获取参数
        val params = buildParameters(criteria)

        // 执行查询
        val totalElements = jdbcTemplate.queryForObject(countSql, params.toTypedArray(), Long::class.java) ?: 0L
        val content = if (totalElements > 0) {
            jdbcTemplate.query(dataSql, params.toTypedArray(), LineageTaskRowMapper())
        } else {
            emptyList()
        }

        return PageImpl(content, pageable, totalElements)
    }

    /**
     * 构建WHERE子句 (Build WHERE clause)
     */
    private fun buildWhereClause(criteria: TaskQueryCriteria): String {
        val conditions = mutableListOf<String>()

        criteria.status?.let { conditions.add("task_status = ?") }
        criteria.taskType?.let { conditions.add("task_type = ?") }
        criteria.taskName?.let { conditions.add("task_name LIKE ?") }
        criteria.createdBy?.let { conditions.add("created_by = ?") }
        criteria.dateFrom?.let { conditions.add("DATE(created_at) >= ?") }
        criteria.dateTo?.let { conditions.add("DATE(created_at) <= ?") }
        criteria.isEnabled?.let { conditions.add("is_enabled = ?") }
        criteria.batchId?.let { conditions.add("batch_id = ?") }

        return if (conditions.isNotEmpty()) {
            "WHERE ${conditions.joinToString(" AND ")}"
        } else {
            ""
        }
    }

    /**
     * 构建ORDER BY子句 (Build ORDER BY clause)
     */
    private fun buildOrderClause(pageable: Pageable): String {
        if (pageable.sort.isUnsorted) {
            return "ORDER BY created_at DESC"
        }

        val orderFields = pageable.sort.map { order ->
            val columnName = mapPropertyToColumn(order.property)
            "$columnName ${order.direction.name}"
        }.joinToString(", ")

        return "ORDER BY $orderFields"
    }

    /**
     * 将属性名映射到数据库列名 (Map property name to database column name)
     */
    private fun mapPropertyToColumn(propertyName: String): String {
        return when (propertyName) {
            "id" -> "id"
            "taskName" -> "task_name"
            "taskType" -> "task_type"
            "sourceType" -> "source_type"
            "sourceIdentifier" -> "source_identifier"
            "sourceContent" -> "source_content"
            "taskStatus" -> "task_status"
            "scheduleType" -> "schedule_type"
            "cronExpression" -> "cron_expression"
            "isEnabled" -> "is_enabled"
            "createdBy" -> "created_by"
            "executedAt" -> "executed_at"
            "completedAt" -> "completed_at"
            "errorMessage" -> "error_message"
            "createdAt" -> "created_at"
            "updatedAt" -> "updated_at"
            "jobKey" -> "job_key"
            "processingTimeMs" -> "processing_time_ms"
            "hasChanges" -> "has_changes"
            "batchId" -> "batch_id"
            "executionCount" -> "execution_count"
            "lastExecutionId" -> "last_execution_id"
            else -> propertyName // 默认返回原属性名 (fallback to original property name)
        }
    }

    /**
     * 构建查询参数 (Build query parameters)
     */
    private fun buildParameters(criteria: TaskQueryCriteria): List<Any> {
        val params = mutableListOf<Any>()

        criteria.status?.let { params.add(it.name) }
        criteria.taskType?.let { params.add(it.name) }
        criteria.taskName?.let { params.add("%$it%") }
        criteria.createdBy?.let { params.add(it) }
        criteria.dateFrom?.let { params.add(it) }
        criteria.dateTo?.let { params.add(it) }
        criteria.isEnabled?.let { params.add(it) }
        criteria.batchId?.let { params.add(it) }

        return params
    }

    /**
     * 更新任务状态和执行信息 (Update task status and execution info)
     */
    fun updateTaskExecution(
        taskId: Long,
        status: TaskStatus,
        executedAt: LocalDateTime?,
        completedAt: LocalDateTime?,
        processingTimeMs: Long?,
        hasChanges: Boolean?,
        errorMessage: String?,
        executionId: String?
    ): Int {
        val sql = """
            UPDATE lineage_tasks 
            SET task_status = ?, 
                executed_at = ?, 
                completed_at = ?, 
                processing_time_ms = ?, 
                has_changes = ?, 
                error_message = ?, 
                last_execution_id = ?,
                execution_count = execution_count + 1,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """.trimIndent()

        return jdbcTemplate.update(
            sql,
            status.name,
            executedAt,
            completedAt,
            processingTimeMs,
            hasChanges,
            errorMessage,
            executionId,
            taskId
        )
    }
    
    /**
     * 更新任务启用状态 (Update task enabled status)
     * 
     * 用于停用不再活跃的数据交换作业对应的血缘任务
     */
    fun updateTaskStatus(
        taskId: Long,
        isEnabled: Boolean,
        updatedBy: String,
        reason: String
    ): Int {
        val sql = """
            UPDATE lineage_tasks 
            SET is_enabled = ?,
                task_status = ?,
                error_message = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """.trimIndent()
        
        // 如果禁用任务，则将状态设置为 DISABLED
        val status = if (!isEnabled) TaskStatus.DISABLED else TaskStatus.PENDING
        
        return jdbcTemplate.update(
            sql,
            isEnabled,
            status.name,
            reason,
            taskId
        )
    }

    /**
     * 血缘任务行映射器 (Lineage Task Row Mapper)
     */
    private class LineageTaskRowMapper : RowMapper<LineageTask> {
        override fun mapRow(rs: ResultSet, rowNum: Int): LineageTask {
            return LineageTask(
                id = rs.getLong("id"),
                taskName = rs.getString("task_name"),
                taskType = TaskType.valueOf(rs.getString("task_type")),
                sourceType = SourceType.valueOf(rs.getString("source_type")),
                sourceIdentifier = rs.getString("source_identifier"),
                sourceContent = rs.getString("source_content"),
                taskStatus = TaskStatus.valueOf(rs.getString("task_status")),
                scheduleType = ScheduleType.valueOf(rs.getString("schedule_type")),
                cronExpression = rs.getString("cron_expression"),
                isEnabled = rs.getBoolean("is_enabled"),
                createdBy = rs.getString("created_by"),
                executedAt = rs.getTimestamp("executed_at")?.toLocalDateTime(),
                completedAt = rs.getTimestamp("completed_at")?.toLocalDateTime(),
                errorMessage = rs.getString("error_message"),
                createdAt = rs.getTimestamp("created_at").toLocalDateTime(),
                updatedAt = rs.getTimestamp("updated_at").toLocalDateTime(),
                jobKey = rs.getString("job_key"),
                processingTimeMs = rs.getObject("processing_time_ms") as? Long,
                hasChanges = rs.getBoolean("has_changes"),
                batchId = rs.getString("batch_id"),
                executionCount = rs.getInt("execution_count"),
                lastExecutionId = rs.getString("last_execution_id")
            )
        }
    }
} 